{"version": 3, "sources": ["../../../../../src/client/components/router-reducer/reducers/refresh-reducer.ts"], "sourcesContent": ["import { fetchServerResponse } from '../fetch-server-response'\nimport { createHrefFromUrl } from '../create-href-from-url'\nimport { applyRouterStatePatchToTree } from '../apply-router-state-patch-to-tree'\nimport { isNavigatingToNewRootLayout } from '../is-navigating-to-new-root-layout'\nimport type {\n  Mutable,\n  ReadonlyReducerState,\n  ReducerState,\n  RefreshAction,\n} from '../router-reducer-types'\nimport { handleExternalUrl } from './navigate-reducer'\nimport { handleMutable } from '../handle-mutable'\nimport type { CacheNode } from '../../../../shared/lib/app-router-context.shared-runtime'\nimport { fillLazyItemsTillLeafWithHead } from '../fill-lazy-items-till-leaf-with-head'\nimport { createEmptyCacheNode } from '../../app-router'\nimport { handleSegmentMismatch } from '../handle-segment-mismatch'\nimport { hasInterceptionRouteInCurrentTree } from './has-interception-route-in-current-tree'\nimport { refreshInactiveParallelSegments } from '../refetch-inactive-parallel-segments'\nimport { revalidateEntireCache } from '../../segment-cache'\n\nexport function refreshReducer(\n  state: ReadonlyReducerState,\n  action: RefreshAction\n): ReducerState {\n  const { origin } = action\n  const mutable: Mutable = {}\n  const href = state.canonicalUrl\n\n  let currentTree = state.tree\n\n  mutable.preserveCustomHistoryState = false\n\n  const cache: CacheNode = createEmptyCacheNode()\n\n  // If the current tree was intercepted, the nextUrl should be included in the request.\n  // This is to ensure that the refresh request doesn't get intercepted, accidentally triggering the interception route.\n  const includeNextUrl = hasInterceptionRouteInCurrentTree(state.tree)\n\n  // TODO-APP: verify that `href` is not an external url.\n  // Fetch data from the root of the tree.\n  cache.lazyData = fetchServerResponse(new URL(href, origin), {\n    flightRouterState: [\n      currentTree[0],\n      currentTree[1],\n      currentTree[2],\n      'refetch',\n    ],\n    nextUrl: includeNextUrl ? state.nextUrl : null,\n  })\n\n  return cache.lazyData.then(\n    async ({ flightData, canonicalUrl: canonicalUrlOverride }) => {\n      // Handle case when navigating to page in `pages` from `app`\n      if (typeof flightData === 'string') {\n        return handleExternalUrl(\n          state,\n          mutable,\n          flightData,\n          state.pushRef.pendingPush\n        )\n      }\n\n      // Remove cache.lazyData as it has been resolved at this point.\n      cache.lazyData = null\n\n      for (const normalizedFlightData of flightData) {\n        const {\n          tree: treePatch,\n          seedData: cacheNodeSeedData,\n          head,\n          isRootRender,\n        } = normalizedFlightData\n\n        if (!isRootRender) {\n          // TODO-APP: handle this case better\n          console.log('REFRESH FAILED')\n          return state\n        }\n\n        const newTree = applyRouterStatePatchToTree(\n          // TODO-APP: remove ''\n          [''],\n          currentTree,\n          treePatch,\n          state.canonicalUrl\n        )\n\n        if (newTree === null) {\n          return handleSegmentMismatch(state, action, treePatch)\n        }\n\n        if (isNavigatingToNewRootLayout(currentTree, newTree)) {\n          return handleExternalUrl(\n            state,\n            mutable,\n            href,\n            state.pushRef.pendingPush\n          )\n        }\n\n        const canonicalUrlOverrideHref = canonicalUrlOverride\n          ? createHrefFromUrl(canonicalUrlOverride)\n          : undefined\n\n        if (canonicalUrlOverride) {\n          mutable.canonicalUrl = canonicalUrlOverrideHref\n        }\n\n        // Handles case where prefetch only returns the router tree patch without rendered components.\n        if (cacheNodeSeedData !== null) {\n          const rsc = cacheNodeSeedData[1]\n          const loading = cacheNodeSeedData[3]\n          cache.rsc = rsc\n          cache.prefetchRsc = null\n          cache.loading = loading\n          fillLazyItemsTillLeafWithHead(\n            cache,\n            // Existing cache is not passed in as `router.refresh()` has to invalidate the entire cache.\n            undefined,\n            treePatch,\n            cacheNodeSeedData,\n            head,\n            undefined\n          )\n          if (process.env.__NEXT_CLIENT_SEGMENT_CACHE) {\n            revalidateEntireCache(state.nextUrl, newTree)\n          } else {\n            mutable.prefetchCache = new Map()\n          }\n        }\n\n        await refreshInactiveParallelSegments({\n          state,\n          updatedTree: newTree,\n          updatedCache: cache,\n          includeNextUrl,\n          canonicalUrl: mutable.canonicalUrl || state.canonicalUrl,\n        })\n\n        mutable.cache = cache\n        mutable.patchedTree = newTree\n\n        currentTree = newTree\n      }\n\n      return handleMutable(state, mutable)\n    },\n    () => state\n  )\n}\n"], "names": ["refreshReducer", "state", "action", "origin", "mutable", "href", "canonicalUrl", "currentTree", "tree", "preserveCustomHistoryState", "cache", "createEmptyCacheNode", "includeNextUrl", "hasInterceptionRouteInCurrentTree", "lazyData", "fetchServerResponse", "URL", "flightRouterState", "nextUrl", "then", "flightData", "canonicalUrlOverride", "handleExternalUrl", "pushRef", "pendingPush", "normalizedFlightData", "treePatch", "seedData", "cacheNodeSeedData", "head", "isRootRender", "console", "log", "newTree", "applyRouterStatePatchToTree", "handleSegmentMismatch", "isNavigatingToNewRootLayout", "canonicalUrlOverrideHref", "createHrefFromUrl", "undefined", "rsc", "loading", "prefetchRsc", "fillLazyItemsTillLeafWithHead", "process", "env", "__NEXT_CLIENT_SEGMENT_CACHE", "revalidateEntireCache", "prefetchCache", "Map", "refreshInactiveParallelSegments", "updatedTree", "updatedCache", "patchedTree", "handleMutable"], "mappings": ";;;;+BAoBgBA;;;eAAAA;;;qCApBoB;mCACF;6CACU;6CACA;iCAOV;+BACJ;+CAEgB;2BACT;uCACC;mDACY;iDACF;8BACV;AAE/B,SAASA,eACdC,KAA2B,EAC3BC,MAAqB;IAErB,MAAM,EAAEC,MAAM,EAAE,GAAGD;IACnB,MAAME,UAAmB,CAAC;IAC1B,MAAMC,OAAOJ,MAAMK,YAAY;IAE/B,IAAIC,cAAcN,MAAMO,IAAI;IAE5BJ,QAAQK,0BAA0B,GAAG;IAErC,MAAMC,QAAmBC,IAAAA,+BAAoB;IAE7C,sFAAsF;IACtF,sHAAsH;IACtH,MAAMC,iBAAiBC,IAAAA,oEAAiC,EAACZ,MAAMO,IAAI;IAEnE,uDAAuD;IACvD,wCAAwC;IACxCE,MAAMI,QAAQ,GAAGC,IAAAA,wCAAmB,EAAC,IAAIC,IAAIX,MAAMF,SAAS;QAC1Dc,mBAAmB;YACjBV,WAAW,CAAC,EAAE;YACdA,WAAW,CAAC,EAAE;YACdA,WAAW,CAAC,EAAE;YACd;SACD;QACDW,SAASN,iBAAiBX,MAAMiB,OAAO,GAAG;IAC5C;IAEA,OAAOR,MAAMI,QAAQ,CAACK,IAAI,CACxB;YAAO,EAAEC,UAAU,EAAEd,cAAce,oBAAoB,EAAE;QACvD,4DAA4D;QAC5D,IAAI,OAAOD,eAAe,UAAU;YAClC,OAAOE,IAAAA,kCAAiB,EACtBrB,OACAG,SACAgB,YACAnB,MAAMsB,OAAO,CAACC,WAAW;QAE7B;QAEA,+DAA+D;QAC/Dd,MAAMI,QAAQ,GAAG;QAEjB,KAAK,MAAMW,wBAAwBL,WAAY;YAC7C,MAAM,EACJZ,MAAMkB,SAAS,EACfC,UAAUC,iBAAiB,EAC3BC,IAAI,EACJC,YAAY,EACb,GAAGL;YAEJ,IAAI,CAACK,cAAc;gBACjB,oCAAoC;gBACpCC,QAAQC,GAAG,CAAC;gBACZ,OAAO/B;YACT;YAEA,MAAMgC,UAAUC,IAAAA,wDAA2B,EACzC,sBAAsB;YACtB;gBAAC;aAAG,EACJ3B,aACAmB,WACAzB,MAAMK,YAAY;YAGpB,IAAI2B,YAAY,MAAM;gBACpB,OAAOE,IAAAA,4CAAqB,EAAClC,OAAOC,QAAQwB;YAC9C;YAEA,IAAIU,IAAAA,wDAA2B,EAAC7B,aAAa0B,UAAU;gBACrD,OAAOX,IAAAA,kCAAiB,EACtBrB,OACAG,SACAC,MACAJ,MAAMsB,OAAO,CAACC,WAAW;YAE7B;YAEA,MAAMa,2BAA2BhB,uBAC7BiB,IAAAA,oCAAiB,EAACjB,wBAClBkB;YAEJ,IAAIlB,sBAAsB;gBACxBjB,QAAQE,YAAY,GAAG+B;YACzB;YAEA,8FAA8F;YAC9F,IAAIT,sBAAsB,MAAM;gBAC9B,MAAMY,MAAMZ,iBAAiB,CAAC,EAAE;gBAChC,MAAMa,UAAUb,iBAAiB,CAAC,EAAE;gBACpClB,MAAM8B,GAAG,GAAGA;gBACZ9B,MAAMgC,WAAW,GAAG;gBACpBhC,MAAM+B,OAAO,GAAGA;gBAChBE,IAAAA,4DAA6B,EAC3BjC,OACA,4FAA4F;gBAC5F6B,WACAb,WACAE,mBACAC,MACAU;gBAEF,IAAIK,QAAQC,GAAG,CAACC,2BAA2B,EAAE;oBAC3CC,IAAAA,mCAAqB,EAAC9C,MAAMiB,OAAO,EAAEe;gBACvC,OAAO;oBACL7B,QAAQ4C,aAAa,GAAG,IAAIC;gBAC9B;YACF;YAEA,MAAMC,IAAAA,gEAA+B,EAAC;gBACpCjD;gBACAkD,aAAalB;gBACbmB,cAAc1C;gBACdE;gBACAN,cAAcF,QAAQE,YAAY,IAAIL,MAAMK,YAAY;YAC1D;YAEAF,QAAQM,KAAK,GAAGA;YAChBN,QAAQiD,WAAW,GAAGpB;YAEtB1B,cAAc0B;QAChB;QAEA,OAAOqB,IAAAA,4BAAa,EAACrD,OAAOG;IAC9B,GACA,IAAMH;AAEV"}