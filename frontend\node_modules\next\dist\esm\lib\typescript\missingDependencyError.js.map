{"version": 3, "sources": ["../../../src/lib/typescript/missingDependencyError.ts"], "sourcesContent": ["import { bold, cyan, red } from '../picocolors'\n\nimport { getOxfordCommaList } from '../oxford-comma-list'\nimport type { MissingDependency } from '../has-necessary-dependencies'\nimport { FatalError } from '../fatal-error'\nimport { getPkgManager } from '../helpers/get-pkg-manager'\n\nexport function missingDepsError(\n  dir: string,\n  missingPackages: MissingDependency[]\n): never {\n  const packagesHuman = getOxfordCommaList(missingPackages.map((p) => p.pkg))\n  const packagesCli = missingPackages.map((p) => p.pkg).join(' ')\n  const packageManager = getPkgManager(dir)\n\n  const removalMsg =\n    '\\n\\n' +\n    bold(\n      'If you are not trying to use TypeScript, please remove the ' +\n        cyan('tsconfig.json') +\n        ' file from your package root (and any TypeScript files in your app and pages directories).'\n    )\n\n  throw new FatalError(\n    bold(\n      red(\n        `It looks like you're trying to use TypeScript but do not have the required package(s) installed.`\n      )\n    ) +\n      '\\n\\n' +\n      bold(`Please install ${bold(packagesHuman)} by running:`) +\n      '\\n\\n' +\n      `\\t${bold(\n        cyan(\n          (packageManager === 'yarn'\n            ? 'yarn add --dev'\n            : packageManager === 'pnpm'\n              ? 'pnpm install --save-dev'\n              : 'npm install --save-dev') +\n            ' ' +\n            packagesCli\n        )\n      )}` +\n      removalMsg +\n      '\\n'\n  )\n}\n"], "names": ["bold", "cyan", "red", "getOxfordCommaList", "FatalE<PERSON>r", "getPkgManager", "missingDepsError", "dir", "missingPackages", "packagesHuman", "map", "p", "pkg", "packagesCli", "join", "packageManager", "removalMsg"], "mappings": "AAAA,SAASA,IAAI,EAAEC,IAAI,EAAEC,GAAG,QAAQ,gBAAe;AAE/C,SAASC,kBAAkB,QAAQ,uBAAsB;AAEzD,SAASC,UAAU,QAAQ,iBAAgB;AAC3C,SAASC,aAAa,QAAQ,6BAA4B;AAE1D,OAAO,SAASC,iBACdC,GAAW,EACXC,eAAoC;IAEpC,MAAMC,gBAAgBN,mBAAmBK,gBAAgBE,GAAG,CAAC,CAACC,IAAMA,EAAEC,GAAG;IACzE,MAAMC,cAAcL,gBAAgBE,GAAG,CAAC,CAACC,IAAMA,EAAEC,GAAG,EAAEE,IAAI,CAAC;IAC3D,MAAMC,iBAAiBV,cAAcE;IAErC,MAAMS,aACJ,SACAhB,KACE,gEACEC,KAAK,mBACL;IAGN,MAAM,qBAsBL,CAtBK,IAAIG,WACRJ,KACEE,IACE,CAAC,gGAAgG,CAAC,KAGpG,SACAF,KAAK,CAAC,eAAe,EAAEA,KAAKS,eAAe,YAAY,CAAC,IACxD,SACA,CAAC,EAAE,EAAET,KACHC,KACE,AAACc,CAAAA,mBAAmB,SAChB,mBACAA,mBAAmB,SACjB,4BACA,wBAAuB,IAC3B,MACAF,eAEH,GACHG,aACA,OArBE,qBAAA;eAAA;oBAAA;sBAAA;IAsBN;AACF"}