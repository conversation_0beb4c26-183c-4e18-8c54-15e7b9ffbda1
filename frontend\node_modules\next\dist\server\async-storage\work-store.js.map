{"version": 3, "sources": ["../../../src/server/async-storage/work-store.ts"], "sourcesContent": ["import type { WorkStore } from '../app-render/work-async-storage.external'\nimport type { IncrementalCache } from '../lib/incremental-cache'\nimport type { RenderOpts } from '../app-render/types'\nimport type { FetchMetric } from '../base-http'\nimport type { RequestLifecycleOpts } from '../base-server'\nimport type { FallbackRouteParams } from '../request/fallback-params'\nimport type { AppSegmentConfig } from '../../build/segment-config/app/app-segment-config'\nimport type { CacheLife } from '../use-cache/cache-life'\n\nimport { AfterContext } from '../after/after-context'\n\nimport { normalizeAppPath } from '../../shared/lib/router/utils/app-paths'\n\nexport type WorkStoreContext = {\n  /**\n   * The page that is being rendered. This relates to the path to the page file.\n   */\n  page: string\n\n  /**\n   * The route parameters that are currently unknown.\n   */\n  fallbackRouteParams: FallbackRouteParams | null\n\n  requestEndedState?: { ended?: boolean }\n  isPrefetchRequest?: boolean\n  renderOpts: {\n    cacheLifeProfiles?: { [profile: string]: CacheLife }\n    incrementalCache?: IncrementalCache\n    isOnDemandRevalidate?: boolean\n    fetchCache?: AppSegmentConfig['fetchCache']\n    isServerAction?: boolean\n    pendingWaitUntil?: Promise<any>\n    experimental: Pick<\n      RenderOpts['experimental'],\n      'isRoutePPREnabled' | 'dynamicIO' | 'authInterrupts'\n    >\n\n    /**\n     * Fetch metrics attached in patch-fetch.ts\n     **/\n    fetchMetrics?: FetchMetric[]\n\n    /**\n     * A hack around accessing the store value outside the context of the\n     * request.\n     *\n     * @internal\n     * @deprecated should only be used as a temporary workaround\n     */\n    // TODO: remove this when we resolve accessing the store outside the execution context\n    store?: WorkStore\n  } & Pick<\n    // Pull some properties from RenderOpts so that the docs are also\n    // mirrored.\n    RenderOpts,\n    | 'assetPrefix'\n    | 'supportsDynamicResponse'\n    | 'shouldWaitOnAllReady'\n    | 'isRevalidate'\n    | 'nextExport'\n    | 'isDraftMode'\n    | 'isDebugDynamicAccesses'\n    | 'dev'\n  > &\n    RequestLifecycleOpts &\n    Partial<Pick<RenderOpts, 'reactLoadableManifest'>>\n\n  /**\n   * The build ID of the current build.\n   */\n  buildId: string\n}\n\nexport function createWorkStore({\n  page,\n  fallbackRouteParams,\n  renderOpts,\n  requestEndedState,\n  isPrefetchRequest,\n  buildId,\n}: WorkStoreContext): WorkStore {\n  /**\n   * Rules of Static & Dynamic HTML:\n   *\n   *    1.) We must generate static HTML unless the caller explicitly opts\n   *        in to dynamic HTML support.\n   *\n   *    2.) If dynamic HTML support is requested, we must honor that request\n   *        or throw an error. It is the sole responsibility of the caller to\n   *        ensure they aren't e.g. requesting dynamic HTML for an AMP page.\n   *\n   *    3.) If the request is in draft mode, we must generate dynamic HTML.\n   *\n   *    4.) If the request is a server action, we must generate dynamic HTML.\n   *\n   * These rules help ensure that other existing features like request caching,\n   * coalescing, and ISR continue working as intended.\n   */\n  const isStaticGeneration =\n    !renderOpts.shouldWaitOnAllReady &&\n    !renderOpts.supportsDynamicResponse &&\n    !renderOpts.isDraftMode &&\n    !renderOpts.isServerAction\n\n  const store: WorkStore = {\n    isStaticGeneration,\n    page,\n    fallbackRouteParams,\n    route: normalizeAppPath(page),\n    incrementalCache:\n      // we fallback to a global incremental cache for edge-runtime locally\n      // so that it can access the fs cache without mocks\n      renderOpts.incrementalCache || (globalThis as any).__incrementalCache,\n    cacheLifeProfiles: renderOpts.cacheLifeProfiles,\n    isRevalidate: renderOpts.isRevalidate,\n    isPrerendering: renderOpts.nextExport,\n    fetchCache: renderOpts.fetchCache,\n    isOnDemandRevalidate: renderOpts.isOnDemandRevalidate,\n\n    isDraftMode: renderOpts.isDraftMode,\n\n    requestEndedState,\n    isPrefetchRequest,\n    buildId,\n    reactLoadableManifest: renderOpts?.reactLoadableManifest || {},\n    assetPrefix: renderOpts?.assetPrefix || '',\n\n    afterContext: createAfterContext(renderOpts),\n    dynamicIOEnabled: renderOpts.experimental.dynamicIO,\n    dev: renderOpts.dev ?? false,\n  }\n\n  // TODO: remove this when we resolve accessing the store outside the execution context\n  renderOpts.store = store\n\n  return store\n}\n\nfunction createAfterContext(renderOpts: RequestLifecycleOpts): AfterContext {\n  const { waitUntil, onClose, onAfterTaskError } = renderOpts\n  return new AfterContext({\n    waitUntil,\n    onClose,\n    onTaskError: onAfterTaskError,\n  })\n}\n"], "names": ["createWorkStore", "page", "fallbackRouteParams", "renderOpts", "requestEndedState", "isPrefetchRequest", "buildId", "isStaticGeneration", "shouldWaitOnAllReady", "supportsDynamicResponse", "isDraftMode", "isServerAction", "store", "route", "normalizeAppPath", "incrementalCache", "globalThis", "__incrementalCache", "cacheLifeProfiles", "isRevalidate", "isPrerendering", "nextExport", "fetchCache", "isOnDemandRevalidate", "reactLoadableManifest", "assetPrefix", "afterContext", "createAfterContext", "dynamicIOEnabled", "experimental", "dynamicIO", "dev", "waitUntil", "onClose", "onAfterTaskError", "AfterContext", "onTaskError"], "mappings": ";;;;+BA0EgBA;;;eAAAA;;;8BAjEa;0BAEI;AA+D1B,SAASA,gBAAgB,EAC9BC,IAAI,EACJC,mBAAmB,EACnBC,UAAU,EACVC,iBAAiB,EACjBC,iBAAiB,EACjBC,OAAO,EACU;IACjB;;;;;;;;;;;;;;;;GAgBC,GACD,MAAMC,qBACJ,CAACJ,WAAWK,oBAAoB,IAChC,CAACL,WAAWM,uBAAuB,IACnC,CAACN,WAAWO,WAAW,IACvB,CAACP,WAAWQ,cAAc;IAE5B,MAAMC,QAAmB;QACvBL;QACAN;QACAC;QACAW,OAAOC,IAAAA,0BAAgB,EAACb;QACxBc,kBACE,qEAAqE;QACrE,mDAAmD;QACnDZ,WAAWY,gBAAgB,IAAI,AAACC,WAAmBC,kBAAkB;QACvEC,mBAAmBf,WAAWe,iBAAiB;QAC/CC,cAAchB,WAAWgB,YAAY;QACrCC,gBAAgBjB,WAAWkB,UAAU;QACrCC,YAAYnB,WAAWmB,UAAU;QACjCC,sBAAsBpB,WAAWoB,oBAAoB;QAErDb,aAAaP,WAAWO,WAAW;QAEnCN;QACAC;QACAC;QACAkB,uBAAuBrB,CAAAA,8BAAAA,WAAYqB,qBAAqB,KAAI,CAAC;QAC7DC,aAAatB,CAAAA,8BAAAA,WAAYsB,WAAW,KAAI;QAExCC,cAAcC,mBAAmBxB;QACjCyB,kBAAkBzB,WAAW0B,YAAY,CAACC,SAAS;QACnDC,KAAK5B,WAAW4B,GAAG,IAAI;IACzB;IAEA,sFAAsF;IACtF5B,WAAWS,KAAK,GAAGA;IAEnB,OAAOA;AACT;AAEA,SAASe,mBAAmBxB,UAAgC;IAC1D,MAAM,EAAE6B,SAAS,EAAEC,OAAO,EAAEC,gBAAgB,EAAE,GAAG/B;IACjD,OAAO,IAAIgC,0BAAY,CAAC;QACtBH;QACAC;QACAG,aAAaF;IACf;AACF"}