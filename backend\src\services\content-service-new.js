const geminiService = require('./gemini.service');

class ContentService {
  async generateStructuredContent(meta, keyword, companyData, newsArticles = [], competitorAnalysis = null, trends = null) {
    try {
      console.log('🎯 Content generation starting');
      const targetWordCount = keyword.wordCount || 2000;
      
      // Generate content using Gemini
      let structuredContent;
      try {
        structuredContent = await geminiService.generateBlogContent(
          meta, 
          keyword, 
          companyData, 
          newsArticles, 
          competitorAnalysis, 
          trends
        );
        console.log('✅ Gemini content generated successfully');
      } catch (geminiError) {
        console.error('❌ Gemini generation failed:', geminiError.message);
        structuredContent = this.generateFallbackContent(meta, keyword.focusKeyword, companyData, targetWordCount);
      }

      return structuredContent;
    } catch (error) {
      console.error('❌ Content generation error:', error);
      throw new Error(`Content generation failed: ${error.message}`);
    }
  }

  generateFallbackContent(meta, keyword, companyData, targetWordCount) {
    return {
      wordCount: targetWordCount,
      introduction: `${meta.metaDescription} This comprehensive guide explores ${keyword} strategies for professional results.`,
      featureImage: {
        alt: `${keyword} professional guide`,
        title: `${meta.h1Title} - Professional Guide`,
        description: `Professional ${keyword} guide image`,
        placement: "after_introduction"
      },
      sections: [
        {
          h2: `Understanding ${keyword}`,
          content: `Professional ${keyword} implementation requires systematic approach and proven methodologies. Industry leaders recognize the importance of ${keyword} in achieving optimal results.`
        },
        {
          h2: `${keyword} Best Practices`,
          content: `Leading companies like ${companyData.companyName} have developed comprehensive frameworks for ${keyword} that deliver consistent results and customer satisfaction.`
        },
        {
          h2: `Implementation Strategies`,
          content: `The integration of modern ${keyword} techniques with traditional practices creates synergistic effects that benefit both contractors and customers.`
        }
      ],
      conclusion: `Mastering ${keyword} requires dedication and commitment to professional excellence. ${companyData.companyName} remains committed to supporting professionals with expert guidance.`,
      references: [
        '"Industry Best Practices" - Professional Standards Authority',
        '"Market Analysis Report" - Industry Research Institute',
        `"Professional Services" - ${companyData.companyName}`
      ]
    };
  }
}

module.exports = new ContentService();
