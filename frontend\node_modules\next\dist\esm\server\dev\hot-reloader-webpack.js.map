{"version": 3, "sources": ["../../../src/server/dev/hot-reloader-webpack.ts"], "sourcesContent": ["import type { NextConfigComplete } from '../config-shared'\nimport type { CustomRoutes } from '../../lib/load-custom-routes'\nimport type { Duplex } from 'stream'\nimport type { Telemetry } from '../../telemetry/storage'\nimport type { IncomingMessage, ServerResponse } from 'http'\nimport type { UrlObject } from 'url'\nimport type { RouteDefinition } from '../route-definitions/route-definition'\n\nimport { webpack, StringXor } from 'next/dist/compiled/webpack/webpack'\nimport {\n  getOverlayMiddleware,\n  getSourceMapMiddleware,\n} from '../../client/components/react-dev-overlay/server/middleware-webpack'\nimport { WebpackHotMiddleware } from './hot-middleware'\nimport { join, relative, isAbsolute, posix } from 'path'\nimport {\n  createEntrypoints,\n  createPagesMapping,\n  finalizeEntrypoint,\n  getClientEntry,\n  getEdgeServerEntry,\n  getAppEntry,\n  runDependingOnPageType,\n  getStaticInfoIncludingLayouts,\n  getInstrumentationEntry,\n} from '../../build/entries'\nimport { watchCompilers } from '../../build/output'\nimport * as Log from '../../build/output/log'\nimport getBaseWebpackConfig, {\n  loadProjectInfo,\n} from '../../build/webpack-config'\nimport { APP_DIR_ALIAS, WEBPACK_LAYERS } from '../../lib/constants'\nimport { recursiveDelete } from '../../lib/recursive-delete'\nimport {\n  BLOCKED_PAGES,\n  CLIENT_STATIC_FILES_RUNTIME_AMP,\n  CLIENT_STATIC_FILES_RUNTIME_MAIN,\n  CLIENT_STATIC_FILES_RUNTIME_MAIN_APP,\n  CLIENT_STATIC_FILES_RUNTIME_REACT_REFRESH,\n  COMPILER_NAMES,\n  RSC_MODULE_TYPES,\n} from '../../shared/lib/constants'\nimport type { __ApiPreviewProps } from '../api-utils'\nimport { getPathMatch } from '../../shared/lib/router/utils/path-match'\nimport { findPageFile } from '../lib/find-page-file'\nimport {\n  BUILDING,\n  getEntries,\n  EntryTypes,\n  getInvalidator,\n  onDemandEntryHandler,\n} from './on-demand-entry-handler'\nimport { denormalizePagePath } from '../../shared/lib/page-path/denormalize-page-path'\nimport { normalizePathSep } from '../../shared/lib/page-path/normalize-path-sep'\nimport getRouteFromEntrypoint from '../get-route-from-entrypoint'\nimport {\n  difference,\n  isInstrumentationHookFile,\n  isMiddlewareFile,\n  isMiddlewareFilename,\n} from '../../build/utils'\nimport { DecodeError } from '../../shared/lib/utils'\nimport { type Span, trace } from '../../trace'\nimport { getProperError } from '../../lib/is-error'\nimport ws from 'next/dist/compiled/ws'\nimport { existsSync, promises as fs } from 'fs'\nimport type { UnwrapPromise } from '../../lib/coalesced-function'\nimport { parseVersionInfo } from './parse-version-info'\nimport type { VersionInfo } from './parse-version-info'\nimport { isAPIRoute } from '../../lib/is-api-route'\nimport { getRouteLoaderEntry } from '../../build/webpack/loaders/next-route-loader'\nimport {\n  isInternalComponent,\n  isNonRoutePagesPage,\n} from '../../lib/is-internal-component'\nimport { RouteKind } from '../route-kind'\nimport {\n  HMR_ACTIONS_SENT_TO_BROWSER,\n  type NextJsHotReloaderInterface,\n} from './hot-reloader-types'\nimport type { HMR_ACTION_TYPES } from './hot-reloader-types'\nimport type { WebpackError } from 'webpack'\nimport { PAGE_TYPES } from '../../lib/page-types'\nimport { FAST_REFRESH_RUNTIME_RELOAD } from './messages'\nimport { getNodeDebugType } from '../lib/utils'\nimport { getNextErrorFeedbackMiddleware } from '../../client/components/react-dev-overlay/server/get-next-error-feedback-middleware'\nimport { getDevOverlayFontMiddleware } from '../../client/components/react-dev-overlay/font/get-dev-overlay-font-middleware'\nimport { getDisableDevIndicatorMiddleware } from './dev-indicator-middleware'\n\nconst MILLISECONDS_IN_NANOSECOND = BigInt(1_000_000)\n\nfunction diff(a: Set<any>, b: Set<any>) {\n  return new Set([...a].filter((v) => !b.has(v)))\n}\n\nconst wsServer = new ws.Server({ noServer: true })\n\nexport async function renderScriptError(\n  res: ServerResponse,\n  error: Error,\n  { verbose = true } = {}\n): Promise<{ finished: true | undefined }> {\n  // Asks CDNs and others to not to cache the errored page\n  res.setHeader(\n    'Cache-Control',\n    'no-cache, no-store, max-age=0, must-revalidate'\n  )\n\n  if ((error as any).code === 'ENOENT') {\n    return { finished: undefined }\n  }\n\n  if (verbose) {\n    console.error(error.stack)\n  }\n  res.statusCode = 500\n  res.end('500 - Internal Error')\n  return { finished: true }\n}\n\nfunction addCorsSupport(req: IncomingMessage, res: ServerResponse) {\n  // Only rewrite CORS handling when URL matches a hot-reloader middleware\n  if (!req.url!.startsWith('/__next')) {\n    return { preflight: false }\n  }\n\n  if (!req.headers.origin) {\n    return { preflight: false }\n  }\n\n  res.setHeader('Access-Control-Allow-Origin', req.headers.origin)\n  res.setHeader('Access-Control-Allow-Methods', 'OPTIONS, GET')\n  // Based on https://github.com/primus/access-control/blob/4cf1bc0e54b086c91e6aa44fb14966fa5ef7549c/index.js#L158\n  if (req.headers['access-control-request-headers']) {\n    res.setHeader(\n      'Access-Control-Allow-Headers',\n      req.headers['access-control-request-headers'] as string\n    )\n  }\n\n  if (req.method === 'OPTIONS') {\n    res.writeHead(200)\n    res.end()\n    return { preflight: true }\n  }\n\n  return { preflight: false }\n}\n\nexport const matchNextPageBundleRequest = getPathMatch(\n  '/_next/static/chunks/pages/:path*.js(\\\\.map|)'\n)\n\n// Iteratively look up the issuer till it ends up at the root\nfunction findEntryModule(\n  module: webpack.Module,\n  compilation: webpack.Compilation\n): any {\n  for (;;) {\n    const issuer = compilation.moduleGraph.getIssuer(module)\n    if (!issuer) return module\n    module = issuer\n  }\n}\n\nfunction erroredPages(compilation: webpack.Compilation) {\n  const failedPages: { [page: string]: WebpackError[] } = {}\n  for (const error of compilation.errors) {\n    if (!error.module) {\n      continue\n    }\n\n    const entryModule = findEntryModule(error.module, compilation)\n    const { name } = entryModule\n    if (!name) {\n      continue\n    }\n\n    // Only pages have to be reloaded\n    const enhancedName = getRouteFromEntrypoint(name)\n\n    if (!enhancedName) {\n      continue\n    }\n\n    if (!failedPages[enhancedName]) {\n      failedPages[enhancedName] = []\n    }\n\n    failedPages[enhancedName].push(error)\n  }\n\n  return failedPages\n}\n\nexport async function getVersionInfo(): Promise<VersionInfo> {\n  let installed = '0.0.0'\n\n  try {\n    installed = require('next/package.json').version\n\n    let res\n\n    try {\n      // use NPM registry regardless user using Yarn\n      res = await fetch('https://registry.npmjs.org/-/package/next/dist-tags')\n    } catch {\n      // ignore fetch errors\n    }\n\n    if (!res || !res.ok) return { installed, staleness: 'unknown' }\n\n    const { latest, canary } = await res.json()\n\n    return parseVersionInfo({ installed, latest, canary })\n  } catch (e: any) {\n    console.error(e)\n    return { installed, staleness: 'unknown' }\n  }\n}\n\nexport default class HotReloaderWebpack implements NextJsHotReloaderInterface {\n  private hasAmpEntrypoints: boolean\n  private hasAppRouterEntrypoints: boolean\n  private hasPagesRouterEntrypoints: boolean\n  private dir: string\n  private buildId: string\n  private encryptionKey: string\n  private middlewares: ((\n    req: IncomingMessage,\n    res: ServerResponse,\n    next: () => void\n  ) => Promise<void>)[]\n  private pagesDir?: string\n  private distDir: string\n  private webpackHotMiddleware?: WebpackHotMiddleware\n  private config: NextConfigComplete\n  private clientStats: webpack.Stats | null\n  private clientError: Error | null = null\n  private serverError: Error | null = null\n  private hmrServerError: Error | null = null\n  private serverPrevDocumentHash: string | null\n  private serverChunkNames?: Set<string>\n  private prevChunkNames?: Set<any>\n  private onDemandEntries?: ReturnType<typeof onDemandEntryHandler>\n  private previewProps: __ApiPreviewProps\n  private watcher: any\n  private rewrites: CustomRoutes['rewrites']\n  private fallbackWatcher: any\n  private hotReloaderSpan: Span\n  private pagesMapping: { [key: string]: string } = {}\n  private appDir?: string\n  private telemetry: Telemetry\n  private resetFetch: () => void\n  private versionInfo: VersionInfo = {\n    staleness: 'unknown',\n    installed: '0.0.0',\n  }\n  private devtoolsFrontendUrl: string | undefined\n  private reloadAfterInvalidation: boolean = false\n\n  public serverStats: webpack.Stats | null\n  public edgeServerStats: webpack.Stats | null\n  public multiCompiler?: webpack.MultiCompiler\n  public activeWebpackConfigs?: Array<\n    UnwrapPromise<ReturnType<typeof getBaseWebpackConfig>>\n  >\n\n  constructor(\n    dir: string,\n    {\n      config,\n      pagesDir,\n      distDir,\n      buildId,\n      encryptionKey,\n      previewProps,\n      rewrites,\n      appDir,\n      telemetry,\n      resetFetch,\n    }: {\n      config: NextConfigComplete\n      pagesDir?: string\n      distDir: string\n      buildId: string\n      encryptionKey: string\n      previewProps: __ApiPreviewProps\n      rewrites: CustomRoutes['rewrites']\n      appDir?: string\n      telemetry: Telemetry\n      resetFetch: () => void\n    }\n  ) {\n    this.hasAmpEntrypoints = false\n    this.hasAppRouterEntrypoints = false\n    this.hasPagesRouterEntrypoints = false\n    this.buildId = buildId\n    this.encryptionKey = encryptionKey\n    this.dir = dir\n    this.middlewares = []\n    this.pagesDir = pagesDir\n    this.appDir = appDir\n    this.distDir = distDir\n    this.clientStats = null\n    this.serverStats = null\n    this.edgeServerStats = null\n    this.serverPrevDocumentHash = null\n    this.telemetry = telemetry\n    this.resetFetch = resetFetch\n\n    this.config = config\n    this.previewProps = previewProps\n    this.rewrites = rewrites\n    this.hotReloaderSpan = trace('hot-reloader', undefined, {\n      version: process.env.__NEXT_VERSION as string,\n    })\n    // Ensure the hotReloaderSpan is flushed immediately as it's the parentSpan for all processing\n    // of the current `next dev` invocation.\n    this.hotReloaderSpan.stop()\n  }\n\n  public async run(\n    req: IncomingMessage,\n    res: ServerResponse,\n    parsedUrl: UrlObject\n  ): Promise<{ finished?: true }> {\n    // Usually CORS support is not needed for the hot-reloader (this is dev only feature)\n    // With when the app runs for multi-zones support behind a proxy,\n    // the current page is trying to access this URL via assetPrefix.\n    // That's when the CORS support is needed.\n    const { preflight } = addCorsSupport(req, res)\n    if (preflight) {\n      return {}\n    }\n\n    // When a request comes in that is a page bundle, e.g. /_next/static/<buildid>/pages/index.js\n    // we have to compile the page using on-demand-entries, this middleware will handle doing that\n    // by adding the page to on-demand-entries, waiting till it's done\n    // and then the bundle will be served like usual by the actual route in server/index.js\n    const handlePageBundleRequest = async (\n      pageBundleRes: ServerResponse,\n      parsedPageBundleUrl: UrlObject\n    ): Promise<{ finished?: true }> => {\n      const { pathname } = parsedPageBundleUrl\n      if (!pathname) return {}\n\n      const params = matchNextPageBundleRequest(pathname)\n      if (!params) return {}\n\n      let decodedPagePath: string\n\n      try {\n        decodedPagePath = `/${params.path\n          .map((param: string) => decodeURIComponent(param))\n          .join('/')}`\n      } catch (_) {\n        throw new DecodeError('failed to decode param')\n      }\n\n      const page = denormalizePagePath(decodedPagePath)\n\n      if (page === '/_error' || BLOCKED_PAGES.indexOf(page) === -1) {\n        try {\n          await this.ensurePage({ page, clientOnly: true, url: req.url })\n        } catch (error) {\n          return await renderScriptError(pageBundleRes, getProperError(error))\n        }\n\n        const errors = await this.getCompilationErrors(page)\n        if (errors.length > 0) {\n          return await renderScriptError(pageBundleRes, errors[0], {\n            verbose: false,\n          })\n        }\n      }\n\n      return {}\n    }\n\n    const { finished } = await handlePageBundleRequest(res, parsedUrl)\n\n    for (const middleware of this.middlewares) {\n      let calledNext = false\n\n      await middleware(req, res, () => {\n        calledNext = true\n      })\n\n      if (!calledNext) {\n        return { finished: true }\n      }\n    }\n\n    return { finished }\n  }\n\n  public setHmrServerError(error: Error | null): void {\n    this.hmrServerError = error\n  }\n\n  public clearHmrServerError(): void {\n    if (this.hmrServerError) {\n      this.setHmrServerError(null)\n      this.send({\n        action: HMR_ACTIONS_SENT_TO_BROWSER.RELOAD_PAGE,\n        data: 'clear hmr server error',\n      })\n    }\n  }\n\n  protected async refreshServerComponents(hash: string): Promise<void> {\n    this.send({\n      action: HMR_ACTIONS_SENT_TO_BROWSER.SERVER_COMPONENT_CHANGES,\n      hash,\n      // TODO: granular reloading of changes\n      // entrypoints: serverComponentChanges,\n    })\n  }\n\n  public onHMR(\n    req: IncomingMessage,\n    _socket: Duplex,\n    head: Buffer,\n    callback: (client: ws.WebSocket) => void\n  ) {\n    wsServer.handleUpgrade(req, req.socket, head, (client) => {\n      this.webpackHotMiddleware?.onHMR(client)\n      this.onDemandEntries?.onHMR(client, () => this.hmrServerError)\n      callback(client)\n\n      client.addEventListener('message', ({ data }) => {\n        data = typeof data !== 'string' ? data.toString() : data\n\n        try {\n          const payload = JSON.parse(data)\n\n          let traceChild:\n            | {\n                name: string\n                startTime?: bigint\n                endTime?: bigint\n                attrs?: Record<string, number | string | undefined | string[]>\n              }\n            | undefined\n\n          switch (payload.event) {\n            case 'span-end': {\n              traceChild = {\n                name: payload.spanName,\n                startTime:\n                  BigInt(Math.floor(payload.startTime)) *\n                  MILLISECONDS_IN_NANOSECOND,\n                attrs: payload.attributes,\n                endTime:\n                  BigInt(Math.floor(payload.endTime)) *\n                  MILLISECONDS_IN_NANOSECOND,\n              }\n              break\n            }\n            case 'client-hmr-latency': {\n              traceChild = {\n                name: payload.event,\n                startTime:\n                  BigInt(payload.startTime) * MILLISECONDS_IN_NANOSECOND,\n                endTime: BigInt(payload.endTime) * MILLISECONDS_IN_NANOSECOND,\n                attrs: {\n                  updatedModules: payload.updatedModules.map((m: string) =>\n                    m\n                      .replace(`(${WEBPACK_LAYERS.appPagesBrowser})/`, '')\n                      .replace(/^\\.\\//, '[project]/')\n                  ),\n                  page: payload.page,\n                  isPageHidden: payload.isPageHidden,\n                },\n              }\n              break\n            }\n            case 'client-reload-page':\n            case 'client-success': {\n              traceChild = {\n                name: payload.event,\n              }\n              break\n            }\n            case 'client-error': {\n              traceChild = {\n                name: payload.event,\n                attrs: { errorCount: payload.errorCount },\n              }\n              break\n            }\n            case 'client-warning': {\n              traceChild = {\n                name: payload.event,\n                attrs: { warningCount: payload.warningCount },\n              }\n              break\n            }\n            case 'client-removed-page':\n            case 'client-added-page': {\n              traceChild = {\n                name: payload.event,\n                attrs: { page: payload.page || '' },\n              }\n              break\n            }\n            case 'client-full-reload': {\n              const { event, stackTrace, hadRuntimeError } = payload\n\n              traceChild = {\n                name: event,\n                attrs: { stackTrace: stackTrace ?? '' },\n              }\n\n              if (hadRuntimeError) {\n                Log.warn(FAST_REFRESH_RUNTIME_RELOAD)\n                break\n              }\n\n              let fileMessage = ''\n              if (stackTrace) {\n                const file = /Aborted because (.+) is not accepted/.exec(\n                  stackTrace\n                )?.[1]\n                if (file) {\n                  // `file` is filepath in `pages/` but it can be a webpack url.\n                  // If it's a webpack loader URL, it will include the app-pages layer\n                  if (file.startsWith(`(${WEBPACK_LAYERS.appPagesBrowser})/`)) {\n                    const fileUrl = new URL(file, 'file://')\n                    const cwd = process.cwd()\n                    const modules = fileUrl.searchParams\n                      .getAll('modules')\n                      .map((filepath) => filepath.slice(cwd.length + 1))\n                      .filter(\n                        (filepath) => !filepath.startsWith('node_modules')\n                      )\n\n                    if (modules.length > 0) {\n                      fileMessage = ` when ${modules.join(', ')} changed`\n                    }\n                  } else if (\n                    // Handle known webpack layers\n                    file.startsWith(`(${WEBPACK_LAYERS.pagesDirBrowser})/`)\n                  ) {\n                    const cleanedFilePath = file.slice(\n                      `(${WEBPACK_LAYERS.pagesDirBrowser})/`.length\n                    )\n\n                    fileMessage = ` when ${cleanedFilePath} changed`\n                  } else {\n                    fileMessage = ` when ${file} changed`\n                  }\n                }\n              }\n\n              Log.warn(\n                `Fast Refresh had to perform a full reload${fileMessage}. Read more: https://nextjs.org/docs/messages/fast-refresh-reload`\n              )\n              break\n            }\n            default: {\n              break\n            }\n          }\n\n          if (traceChild) {\n            this.hotReloaderSpan.manualTraceChild(\n              traceChild.name,\n              traceChild.startTime,\n              traceChild.endTime,\n              { ...traceChild.attrs, clientId: payload.id }\n            )\n          }\n        } catch (_) {\n          // invalid WebSocket message\n        }\n      })\n    })\n  }\n\n  private async clean(span: Span): Promise<void> {\n    return span\n      .traceChild('clean')\n      .traceAsyncFn(() =>\n        recursiveDelete(join(this.dir, this.config.distDir), /^cache/)\n      )\n  }\n\n  private async getWebpackConfig(span: Span) {\n    const webpackConfigSpan = span.traceChild('get-webpack-config')\n\n    const pageExtensions = this.config.pageExtensions\n\n    return webpackConfigSpan.traceAsyncFn(async () => {\n      const pagePaths = !this.pagesDir\n        ? ([] as (string | null)[])\n        : await webpackConfigSpan\n            .traceChild('get-page-paths')\n            .traceAsyncFn(() =>\n              Promise.all([\n                findPageFile(this.pagesDir!, '/_app', pageExtensions, false),\n                findPageFile(\n                  this.pagesDir!,\n                  '/_document',\n                  pageExtensions,\n                  false\n                ),\n              ])\n            )\n\n      this.pagesMapping = await webpackConfigSpan\n        .traceChild('create-pages-mapping')\n        .traceAsyncFn(() =>\n          createPagesMapping({\n            isDev: true,\n            pageExtensions: this.config.pageExtensions,\n            pagesType: PAGE_TYPES.PAGES,\n            pagePaths: pagePaths.filter(\n              (i: string | null): i is string => typeof i === 'string'\n            ),\n            pagesDir: this.pagesDir,\n            appDir: this.appDir,\n          })\n        )\n\n      const entrypoints = await webpackConfigSpan\n        .traceChild('create-entrypoints')\n        .traceAsyncFn(() =>\n          createEntrypoints({\n            appDir: this.appDir,\n            buildId: this.buildId,\n            config: this.config,\n            envFiles: [],\n            isDev: true,\n            pages: this.pagesMapping,\n            pagesDir: this.pagesDir,\n            previewMode: this.previewProps,\n            rootDir: this.dir,\n            pageExtensions: this.config.pageExtensions,\n          })\n        )\n\n      const commonWebpackOptions = {\n        dev: true,\n        buildId: this.buildId,\n        encryptionKey: this.encryptionKey,\n        config: this.config,\n        pagesDir: this.pagesDir,\n        rewrites: this.rewrites,\n        originalRewrites: this.config._originalRewrites,\n        originalRedirects: this.config._originalRedirects,\n        runWebpackSpan: this.hotReloaderSpan,\n        appDir: this.appDir,\n      }\n\n      return webpackConfigSpan\n        .traceChild('generate-webpack-config')\n        .traceAsyncFn(async () => {\n          const info = await loadProjectInfo({\n            dir: this.dir,\n            config: commonWebpackOptions.config,\n            dev: true,\n          })\n          return Promise.all([\n            // order is important here\n            getBaseWebpackConfig(this.dir, {\n              ...commonWebpackOptions,\n              compilerType: COMPILER_NAMES.client,\n              entrypoints: entrypoints.client,\n              ...info,\n            }),\n            getBaseWebpackConfig(this.dir, {\n              ...commonWebpackOptions,\n              compilerType: COMPILER_NAMES.server,\n              entrypoints: entrypoints.server,\n              ...info,\n            }),\n            getBaseWebpackConfig(this.dir, {\n              ...commonWebpackOptions,\n              compilerType: COMPILER_NAMES.edgeServer,\n              entrypoints: entrypoints.edgeServer,\n              ...info,\n            }),\n          ])\n        })\n    })\n  }\n\n  public async buildFallbackError(): Promise<void> {\n    if (this.fallbackWatcher) return\n\n    const info = await loadProjectInfo({\n      dir: this.dir,\n      config: this.config,\n      dev: true,\n    })\n    const fallbackConfig = await getBaseWebpackConfig(this.dir, {\n      runWebpackSpan: this.hotReloaderSpan,\n      dev: true,\n      compilerType: COMPILER_NAMES.client,\n      config: this.config,\n      buildId: this.buildId,\n      encryptionKey: this.encryptionKey,\n      appDir: this.appDir,\n      pagesDir: this.pagesDir,\n      rewrites: {\n        beforeFiles: [],\n        afterFiles: [],\n        fallback: [],\n      },\n      originalRewrites: {\n        beforeFiles: [],\n        afterFiles: [],\n        fallback: [],\n      },\n      originalRedirects: [],\n      isDevFallback: true,\n      entrypoints: (\n        await createEntrypoints({\n          appDir: this.appDir,\n          buildId: this.buildId,\n          config: this.config,\n          envFiles: [],\n          isDev: true,\n          pages: {\n            '/_app': 'next/dist/pages/_app',\n            '/_error': 'next/dist/pages/_error',\n          },\n          pagesDir: this.pagesDir,\n          previewMode: this.previewProps,\n          rootDir: this.dir,\n          pageExtensions: this.config.pageExtensions,\n        })\n      ).client,\n      ...info,\n    })\n    const fallbackCompiler = webpack(fallbackConfig)\n\n    this.fallbackWatcher = await new Promise((resolve) => {\n      let bootedFallbackCompiler = false\n      fallbackCompiler.watch(\n        // @ts-ignore webpack supports an array of watchOptions when using a multiCompiler\n        fallbackConfig.watchOptions,\n        // Errors are handled separately\n        (_err: any) => {\n          if (!bootedFallbackCompiler) {\n            bootedFallbackCompiler = true\n            resolve(true)\n          }\n        }\n      )\n    })\n  }\n\n  private async tracedGetVersionInfo(span: Span) {\n    const versionInfoSpan = span.traceChild('get-version-info')\n    return versionInfoSpan.traceAsyncFn<VersionInfo>(async () =>\n      getVersionInfo()\n    )\n  }\n\n  public async start(): Promise<void> {\n    const startSpan = this.hotReloaderSpan.traceChild('start')\n    startSpan.stop() // Stop immediately to create an artificial parent span\n\n    this.versionInfo = await this.tracedGetVersionInfo(startSpan)\n\n    const nodeDebugType = getNodeDebugType()\n    if (nodeDebugType && !this.devtoolsFrontendUrl) {\n      const debugPort = process.debugPort\n      let debugInfo\n      try {\n        // It requires to use 127.0.0.1 instead of localhost for server-side fetching.\n        const debugInfoList = await fetch(\n          `http://127.0.0.1:${debugPort}/json/list`\n        ).then((res) => res.json())\n        // There will be only one item for current process, so always get the first item.\n        debugInfo = debugInfoList[0]\n      } catch {}\n      if (debugInfo) {\n        this.devtoolsFrontendUrl = debugInfo.devtoolsFrontendUrl\n      }\n    }\n\n    await this.clean(startSpan)\n    // Ensure distDir exists before writing package.json\n    await fs.mkdir(this.distDir, { recursive: true })\n\n    const distPackageJsonPath = join(this.distDir, 'package.json')\n    // Ensure commonjs handling is used for files in the distDir (generally .next)\n    // Files outside of the distDir can be \"type\": \"module\"\n    await fs.writeFile(distPackageJsonPath, '{\"type\": \"commonjs\"}')\n\n    this.activeWebpackConfigs = await this.getWebpackConfig(startSpan)\n\n    for (const config of this.activeWebpackConfigs) {\n      const defaultEntry = config.entry\n      config.entry = async (...args) => {\n        const outputPath = this.multiCompiler?.outputPath || ''\n        const entries = getEntries(outputPath)\n        // @ts-ignore entry is always a function\n        const entrypoints = await defaultEntry(...args)\n        const isClientCompilation = config.name === COMPILER_NAMES.client\n        const isNodeServerCompilation = config.name === COMPILER_NAMES.server\n        const isEdgeServerCompilation =\n          config.name === COMPILER_NAMES.edgeServer\n\n        await Promise.all(\n          Object.keys(entries).map(async (entryKey) => {\n            const entryData = entries[entryKey]\n            const { bundlePath, dispose } = entryData\n\n            const result =\n              /^(client|server|edge-server)@(app|pages|root)@(.*)/g.exec(\n                entryKey\n              )\n            const [, key /* pageType */, , page] = result! // this match should always happen\n\n            if (key === COMPILER_NAMES.client && !isClientCompilation) return\n            if (key === COMPILER_NAMES.server && !isNodeServerCompilation)\n              return\n            if (key === COMPILER_NAMES.edgeServer && !isEdgeServerCompilation)\n              return\n\n            const isEntry = entryData.type === EntryTypes.ENTRY\n            const isChildEntry = entryData.type === EntryTypes.CHILD_ENTRY\n\n            // Check if the page was removed or disposed and remove it\n            if (isEntry) {\n              const pageExists =\n                !dispose && existsSync(entryData.absolutePagePath)\n              if (!pageExists) {\n                delete entries[entryKey]\n                return\n              }\n            }\n\n            // For child entries, if it has an entry file and it's gone, remove it\n            if (isChildEntry) {\n              if (entryData.absoluteEntryFilePath) {\n                const pageExists =\n                  !dispose && existsSync(entryData.absoluteEntryFilePath)\n                if (!pageExists) {\n                  delete entries[entryKey]\n                  return\n                }\n              }\n            }\n\n            // Ensure _error is considered a `pages` page.\n            if (page === '/_error') {\n              this.hasPagesRouterEntrypoints = true\n            }\n\n            const hasAppDir = !!this.appDir\n            const isAppPath = hasAppDir && bundlePath.startsWith('app/')\n            const staticInfo = isEntry\n              ? await getStaticInfoIncludingLayouts({\n                  isInsideAppDir: isAppPath,\n                  pageExtensions: this.config.pageExtensions,\n                  pageFilePath: entryData.absolutePagePath,\n                  appDir: this.appDir,\n                  config: this.config,\n                  isDev: true,\n                  page,\n                })\n              : undefined\n\n            if (staticInfo?.type === PAGE_TYPES.PAGES) {\n              if (\n                staticInfo.config?.config?.amp === true ||\n                staticInfo.config?.config?.amp === 'hybrid'\n              ) {\n                this.hasAmpEntrypoints = true\n              }\n            }\n\n            const isServerComponent =\n              isAppPath && staticInfo?.rsc !== RSC_MODULE_TYPES.client\n\n            const pageType: PAGE_TYPES = entryData.bundlePath.startsWith(\n              'pages/'\n            )\n              ? PAGE_TYPES.PAGES\n              : entryData.bundlePath.startsWith('app/')\n                ? PAGE_TYPES.APP\n                : PAGE_TYPES.ROOT\n\n            if (pageType === 'pages') {\n              this.hasPagesRouterEntrypoints = true\n            }\n            if (pageType === 'app') {\n              this.hasAppRouterEntrypoints = true\n            }\n\n            const isInstrumentation =\n              isInstrumentationHookFile(page) && pageType === PAGE_TYPES.ROOT\n\n            let pageRuntime = staticInfo?.runtime\n\n            if (\n              isMiddlewareFile(page) &&\n              !this.config.experimental.nodeMiddleware &&\n              pageRuntime === 'nodejs'\n            ) {\n              Log.warn(\n                'nodejs runtime support for middleware requires experimental.nodeMiddleware be enabled in your next.config'\n              )\n              pageRuntime = 'edge'\n            }\n\n            runDependingOnPageType({\n              page,\n              pageRuntime,\n              pageType,\n              onEdgeServer: () => {\n                // TODO-APP: verify if child entry should support.\n                if (!isEdgeServerCompilation || !isEntry) return\n                entries[entryKey].status = BUILDING\n\n                if (isInstrumentation) {\n                  const normalizedBundlePath = bundlePath.replace('src/', '')\n                  entrypoints[normalizedBundlePath] = finalizeEntrypoint({\n                    compilerType: COMPILER_NAMES.edgeServer,\n                    name: normalizedBundlePath,\n                    value: getInstrumentationEntry({\n                      absolutePagePath: entryData.absolutePagePath,\n                      isEdgeServer: true,\n                      isDev: true,\n                    }),\n                    isServerComponent: true,\n                    hasAppDir,\n                  })\n                  return\n                }\n                const appDirLoader = isAppPath\n                  ? getAppEntry({\n                      name: bundlePath,\n                      page,\n                      appPaths: entryData.appPaths,\n                      pagePath: posix.join(\n                        APP_DIR_ALIAS,\n                        relative(\n                          this.appDir!,\n                          entryData.absolutePagePath\n                        ).replace(/\\\\/g, '/')\n                      ),\n                      appDir: this.appDir!,\n                      pageExtensions: this.config.pageExtensions,\n                      rootDir: this.dir,\n                      isDev: true,\n                      tsconfigPath: this.config.typescript.tsconfigPath,\n                      basePath: this.config.basePath,\n                      assetPrefix: this.config.assetPrefix,\n                      nextConfigOutput: this.config.output,\n                      preferredRegion: staticInfo?.preferredRegion,\n                      middlewareConfig: Buffer.from(\n                        JSON.stringify(staticInfo?.middleware || {})\n                      ).toString('base64'),\n                    }).import\n                  : undefined\n\n                entrypoints[bundlePath] = finalizeEntrypoint({\n                  compilerType: COMPILER_NAMES.edgeServer,\n                  name: bundlePath,\n                  value: getEdgeServerEntry({\n                    absolutePagePath: entryData.absolutePagePath,\n                    rootDir: this.dir,\n                    buildId: this.buildId,\n                    bundlePath,\n                    config: this.config,\n                    isDev: true,\n                    page,\n                    pages: this.pagesMapping,\n                    isServerComponent,\n                    appDirLoader,\n                    pagesType: isAppPath ? PAGE_TYPES.APP : PAGE_TYPES.PAGES,\n                    preferredRegion: staticInfo?.preferredRegion,\n                  }),\n                  hasAppDir,\n                })\n              },\n              onClient: () => {\n                if (!isClientCompilation) return\n                if (isChildEntry) {\n                  entries[entryKey].status = BUILDING\n                  entrypoints[bundlePath] = finalizeEntrypoint({\n                    name: bundlePath,\n                    compilerType: COMPILER_NAMES.client,\n                    value: entryData.request,\n                    hasAppDir,\n                  })\n                } else {\n                  entries[entryKey].status = BUILDING\n                  entrypoints[bundlePath] = finalizeEntrypoint({\n                    name: bundlePath,\n                    compilerType: COMPILER_NAMES.client,\n                    value: getClientEntry({\n                      absolutePagePath: entryData.absolutePagePath,\n                      page,\n                    }),\n                    hasAppDir,\n                  })\n                }\n              },\n              onServer: () => {\n                // TODO-APP: verify if child entry should support.\n                if (!isNodeServerCompilation || !isEntry) return\n                entries[entryKey].status = BUILDING\n                let relativeRequest = relative(\n                  config.context!,\n                  entryData.absolutePagePath\n                )\n                if (\n                  !isAbsolute(relativeRequest) &&\n                  !relativeRequest.startsWith('../')\n                ) {\n                  relativeRequest = `./${relativeRequest}`\n                }\n\n                let value: { import: string; layer?: string } | string\n                if (isInstrumentation) {\n                  value = getInstrumentationEntry({\n                    absolutePagePath: entryData.absolutePagePath,\n                    isEdgeServer: false,\n                    isDev: true,\n                  })\n                  entrypoints[bundlePath] = finalizeEntrypoint({\n                    compilerType: COMPILER_NAMES.server,\n                    name: bundlePath,\n                    isServerComponent: true,\n                    value,\n                    hasAppDir,\n                  })\n                } else if (isMiddlewareFile(page)) {\n                  value = getEdgeServerEntry({\n                    absolutePagePath: entryData.absolutePagePath,\n                    rootDir: this.dir,\n                    buildId: this.buildId,\n                    bundlePath,\n                    config: this.config,\n                    isDev: true,\n                    page,\n                    pages: this.pagesMapping,\n                    isServerComponent,\n                    pagesType: PAGE_TYPES.PAGES,\n                    preferredRegion: staticInfo?.preferredRegion,\n                  })\n                } else if (isAppPath) {\n                  value = getAppEntry({\n                    name: bundlePath,\n                    page,\n                    appPaths: entryData.appPaths,\n                    pagePath: posix.join(\n                      APP_DIR_ALIAS,\n                      relative(\n                        this.appDir!,\n                        entryData.absolutePagePath\n                      ).replace(/\\\\/g, '/')\n                    ),\n                    appDir: this.appDir!,\n                    pageExtensions: this.config.pageExtensions,\n                    rootDir: this.dir,\n                    isDev: true,\n                    tsconfigPath: this.config.typescript.tsconfigPath,\n                    basePath: this.config.basePath,\n                    assetPrefix: this.config.assetPrefix,\n                    nextConfigOutput: this.config.output,\n                    preferredRegion: staticInfo?.preferredRegion,\n                    middlewareConfig: Buffer.from(\n                      JSON.stringify(staticInfo?.middleware || {})\n                    ).toString('base64'),\n                  })\n                } else if (isAPIRoute(page)) {\n                  value = getRouteLoaderEntry({\n                    kind: RouteKind.PAGES_API,\n                    page,\n                    absolutePagePath: relativeRequest,\n                    preferredRegion: staticInfo?.preferredRegion,\n                    middlewareConfig: staticInfo?.middleware || {},\n                  })\n                } else if (\n                  !isMiddlewareFile(page) &&\n                  !isInternalComponent(relativeRequest) &&\n                  !isNonRoutePagesPage(page) &&\n                  !isInstrumentation\n                ) {\n                  value = getRouteLoaderEntry({\n                    kind: RouteKind.PAGES,\n                    page,\n                    pages: this.pagesMapping,\n                    absolutePagePath: relativeRequest,\n                    preferredRegion: staticInfo?.preferredRegion,\n                    middlewareConfig: staticInfo?.middleware ?? {},\n                  })\n                } else {\n                  value = relativeRequest\n                }\n\n                entrypoints[bundlePath] = finalizeEntrypoint({\n                  compilerType: COMPILER_NAMES.server,\n                  name: bundlePath,\n                  isServerComponent,\n                  value,\n                  hasAppDir,\n                })\n              },\n            })\n          })\n        )\n\n        if (!this.hasAmpEntrypoints) {\n          delete entrypoints[CLIENT_STATIC_FILES_RUNTIME_AMP]\n        }\n        if (!this.hasPagesRouterEntrypoints) {\n          delete entrypoints[CLIENT_STATIC_FILES_RUNTIME_MAIN]\n          delete entrypoints['pages/_app']\n          delete entrypoints['pages/_error']\n          delete entrypoints['/_error']\n          delete entrypoints['pages/_document']\n        }\n        // Remove React Refresh entrypoint chunk as `app` doesn't require it.\n        if (!this.hasAmpEntrypoints && !this.hasPagesRouterEntrypoints) {\n          delete entrypoints[CLIENT_STATIC_FILES_RUNTIME_REACT_REFRESH]\n        }\n        if (!this.hasAppRouterEntrypoints) {\n          delete entrypoints[CLIENT_STATIC_FILES_RUNTIME_MAIN_APP]\n        }\n\n        return entrypoints\n      }\n    }\n\n    // Enable building of client compilation before server compilation in development\n    // @ts-ignore webpack 5\n    this.activeWebpackConfigs.parallelism = 1\n\n    this.multiCompiler = webpack(\n      this.activeWebpackConfigs\n    ) as unknown as webpack.MultiCompiler\n\n    // Copy over the filesystem so that it is shared between all compilers.\n    const inputFileSystem = this.multiCompiler.compilers[0].inputFileSystem\n    for (const compiler of this.multiCompiler.compilers) {\n      compiler.inputFileSystem = inputFileSystem\n      // This is set for the initial compile. After that Watching class in webpack adds it.\n      compiler.fsStartTime = Date.now()\n      // Ensure NodeEnvironmentPlugin doesn't purge the inputFileSystem. Purging is handled in `done` below.\n      compiler.hooks.beforeRun.intercept({\n        register(tapInfo: any) {\n          if (tapInfo.name === 'NodeEnvironmentPlugin') {\n            return null\n          }\n          return tapInfo\n        },\n      })\n    }\n\n    this.multiCompiler.hooks.done.tap('NextjsHotReloader', () => {\n      inputFileSystem?.purge?.()\n    })\n    watchCompilers(\n      this.multiCompiler.compilers[0],\n      this.multiCompiler.compilers[1],\n      this.multiCompiler.compilers[2]\n    )\n\n    // Watch for changes to client/server page files so we can tell when just\n    // the server file changes and trigger a reload for GS(S)P pages\n    const changedClientPages = new Set<string>()\n    const changedServerPages = new Set<string>()\n    const changedEdgeServerPages = new Set<string>()\n\n    const changedServerComponentPages = new Set<string>()\n    const changedCSSImportPages = new Set<string>()\n\n    const prevClientPageHashes = new Map<string, string>()\n    const prevServerPageHashes = new Map<string, string>()\n    const prevEdgeServerPageHashes = new Map<string, string>()\n    const prevCSSImportModuleHashes = new Map<string, string>()\n\n    const pageExtensionRegex = new RegExp(\n      `\\\\.(?:${this.config.pageExtensions.join('|')})$`\n    )\n\n    const trackPageChanges =\n      (\n        pageHashMap: Map<string, string>,\n        changedItems: Set<string>,\n        serverComponentChangedItems?: Set<string>\n      ) =>\n      (stats: webpack.Compilation) => {\n        try {\n          stats.entrypoints.forEach((entry, key) => {\n            if (\n              key.startsWith('pages/') ||\n              key.startsWith('app/') ||\n              isMiddlewareFilename(key)\n            ) {\n              // TODO this doesn't handle on demand loaded chunks\n              entry.chunks.forEach((chunk) => {\n                if (chunk.id === key) {\n                  const modsIterable: any =\n                    stats.chunkGraph.getChunkModulesIterable(chunk)\n\n                  let hasCSSModuleChanges = false\n                  let chunksHash = new StringXor()\n                  let chunksHashServerLayer = new StringXor()\n\n                  modsIterable.forEach((mod: any) => {\n                    if (\n                      mod.resource &&\n                      mod.resource.replace(/\\\\/g, '/').includes(key) &&\n                      // Shouldn't match CSS modules, etc.\n                      pageExtensionRegex.test(mod.resource)\n                    ) {\n                      // use original source to calculate hash since mod.hash\n                      // includes the source map in development which changes\n                      // every time for both server and client so we calculate\n                      // the hash without the source map for the page module\n                      const hash = require('crypto')\n                        .createHash('sha1')\n                        .update(mod.originalSource().buffer())\n                        .digest()\n                        .toString('hex')\n\n                      if (\n                        mod.layer === WEBPACK_LAYERS.reactServerComponents &&\n                        mod?.buildInfo?.rsc?.type !== 'client'\n                      ) {\n                        chunksHashServerLayer.add(hash)\n                      }\n\n                      chunksHash.add(hash)\n                    } else {\n                      // for non-pages we can use the module hash directly\n                      const hash = stats.chunkGraph.getModuleHash(\n                        mod,\n                        chunk.runtime\n                      )\n\n                      if (\n                        mod.layer === WEBPACK_LAYERS.reactServerComponents &&\n                        mod?.buildInfo?.rsc?.type !== 'client'\n                      ) {\n                        chunksHashServerLayer.add(hash)\n                      }\n\n                      chunksHash.add(hash)\n\n                      // Both CSS import changes from server and client\n                      // components are tracked.\n                      if (\n                        key.startsWith('app/') &&\n                        /\\.(css|scss|sass)$/.test(mod.resource || '')\n                      ) {\n                        const resourceKey = mod.layer + ':' + mod.resource\n                        const prevHash =\n                          prevCSSImportModuleHashes.get(resourceKey)\n                        if (prevHash && prevHash !== hash) {\n                          hasCSSModuleChanges = true\n                        }\n                        prevCSSImportModuleHashes.set(resourceKey, hash)\n                      }\n                    }\n                  })\n\n                  const prevHash = pageHashMap.get(key)\n                  const curHash = chunksHash.toString()\n                  if (prevHash && prevHash !== curHash) {\n                    changedItems.add(key)\n                  }\n                  pageHashMap.set(key, curHash)\n\n                  if (serverComponentChangedItems) {\n                    const serverKey =\n                      WEBPACK_LAYERS.reactServerComponents + ':' + key\n                    const prevServerHash = pageHashMap.get(serverKey)\n                    const curServerHash = chunksHashServerLayer.toString()\n                    if (prevServerHash && prevServerHash !== curServerHash) {\n                      serverComponentChangedItems.add(key)\n                    }\n                    pageHashMap.set(serverKey, curServerHash)\n                  }\n\n                  if (hasCSSModuleChanges) {\n                    changedCSSImportPages.add(key)\n                  }\n                }\n              })\n            }\n          })\n        } catch (err) {\n          console.error(err)\n        }\n      }\n\n    this.multiCompiler.compilers[0].hooks.emit.tap(\n      'NextjsHotReloaderForClient',\n      trackPageChanges(prevClientPageHashes, changedClientPages)\n    )\n    this.multiCompiler.compilers[1].hooks.emit.tap(\n      'NextjsHotReloaderForServer',\n      trackPageChanges(\n        prevServerPageHashes,\n        changedServerPages,\n        changedServerComponentPages\n      )\n    )\n    this.multiCompiler.compilers[2].hooks.emit.tap(\n      'NextjsHotReloaderForServer',\n      trackPageChanges(\n        prevEdgeServerPageHashes,\n        changedEdgeServerPages,\n        changedServerComponentPages\n      )\n    )\n\n    // This plugin watches for changes to _document.js and notifies the client side that it should reload the page\n    this.multiCompiler.compilers[1].hooks.failed.tap(\n      'NextjsHotReloaderForServer',\n      (err: Error) => {\n        this.serverError = err\n        this.serverStats = null\n        this.serverChunkNames = undefined\n      }\n    )\n\n    this.multiCompiler.compilers[2].hooks.done.tap(\n      'NextjsHotReloaderForServer',\n      (stats) => {\n        this.serverError = null\n        this.edgeServerStats = stats\n      }\n    )\n\n    this.multiCompiler.compilers[1].hooks.done.tap(\n      'NextjsHotReloaderForServer',\n      (stats) => {\n        this.serverError = null\n        this.serverStats = stats\n\n        if (!this.pagesDir) {\n          return\n        }\n\n        const { compilation } = stats\n\n        // We only watch `_document` for changes on the server compilation\n        // the rest of the files will be triggered by the client compilation\n        const documentChunk = compilation.namedChunks.get('pages/_document')\n        // If the document chunk can't be found we do nothing\n        if (!documentChunk) {\n          return\n        }\n\n        // Initial value\n        if (this.serverPrevDocumentHash === null) {\n          this.serverPrevDocumentHash = documentChunk.hash || null\n          return\n        }\n\n        // If _document.js didn't change we don't trigger a reload.\n        if (documentChunk.hash === this.serverPrevDocumentHash) {\n          return\n        }\n\n        // As document chunk will change if new app pages are joined,\n        // since react bundle is different it will effect the chunk hash.\n        // So we diff the chunk changes, if there's only new app page chunk joins,\n        // then we don't trigger a reload by checking pages/_document chunk change.\n        if (this.appDir) {\n          const chunkNames = new Set(compilation.namedChunks.keys())\n          const diffChunkNames = difference<string>(\n            this.serverChunkNames || new Set(),\n            chunkNames\n          )\n\n          if (\n            diffChunkNames.length === 0 ||\n            diffChunkNames.every((chunkName) => chunkName.startsWith('app/'))\n          ) {\n            return\n          }\n          this.serverChunkNames = chunkNames\n        }\n\n        this.serverPrevDocumentHash = documentChunk.hash || null\n\n        // Notify reload to reload the page, as _document.js was changed (different hash)\n        this.send({\n          action: HMR_ACTIONS_SENT_TO_BROWSER.RELOAD_PAGE,\n          data: '_document has changed',\n        })\n      }\n    )\n\n    this.multiCompiler.hooks.done.tap('NextjsHotReloaderForServer', (stats) => {\n      const reloadAfterInvalidation = this.reloadAfterInvalidation\n      this.reloadAfterInvalidation = false\n\n      const serverOnlyChanges = difference<string>(\n        changedServerPages,\n        changedClientPages\n      )\n\n      const edgeServerOnlyChanges = difference<string>(\n        changedEdgeServerPages,\n        changedClientPages\n      )\n\n      const pageChanges = serverOnlyChanges\n        .concat(edgeServerOnlyChanges)\n        .filter((key) => key.startsWith('pages/'))\n\n      const middlewareChanges = [\n        ...Array.from(changedEdgeServerPages),\n        ...Array.from(changedServerPages),\n      ].filter((name) => isMiddlewareFilename(name))\n\n      if (middlewareChanges.length > 0) {\n        this.send({\n          event: HMR_ACTIONS_SENT_TO_BROWSER.MIDDLEWARE_CHANGES,\n        })\n      }\n\n      if (pageChanges.length > 0) {\n        this.send({\n          event: HMR_ACTIONS_SENT_TO_BROWSER.SERVER_ONLY_CHANGES,\n          pages: serverOnlyChanges.map((pg) =>\n            denormalizePagePath(pg.slice('pages'.length))\n          ),\n        })\n      }\n\n      if (\n        changedServerComponentPages.size ||\n        changedCSSImportPages.size ||\n        reloadAfterInvalidation\n      ) {\n        this.resetFetch()\n        this.refreshServerComponents(stats.hash)\n      }\n\n      changedClientPages.clear()\n      changedServerPages.clear()\n      changedEdgeServerPages.clear()\n      changedServerComponentPages.clear()\n      changedCSSImportPages.clear()\n    })\n\n    this.multiCompiler.compilers[0].hooks.failed.tap(\n      'NextjsHotReloaderForClient',\n      (err: Error) => {\n        this.clientError = err\n        this.clientStats = null\n      }\n    )\n    this.multiCompiler.compilers[0].hooks.done.tap(\n      'NextjsHotReloaderForClient',\n      (stats) => {\n        this.clientError = null\n        this.clientStats = stats\n\n        const { compilation } = stats\n        const chunkNames = new Set(\n          [...compilation.namedChunks.keys()].filter(\n            (name) => !!getRouteFromEntrypoint(name)\n          )\n        )\n\n        if (this.prevChunkNames) {\n          // detect chunks which have to be replaced with a new template\n          // e.g, pages/index.js <-> pages/_error.js\n          const addedPages = diff(chunkNames, this.prevChunkNames!)\n          const removedPages = diff(this.prevChunkNames!, chunkNames)\n\n          if (addedPages.size > 0) {\n            for (const addedPage of addedPages) {\n              const page = getRouteFromEntrypoint(addedPage)\n              this.send({\n                action: HMR_ACTIONS_SENT_TO_BROWSER.ADDED_PAGE,\n                data: [page],\n              })\n            }\n          }\n\n          if (removedPages.size > 0) {\n            for (const removedPage of removedPages) {\n              const page = getRouteFromEntrypoint(removedPage)\n              this.send({\n                action: HMR_ACTIONS_SENT_TO_BROWSER.REMOVED_PAGE,\n                data: [page],\n              })\n            }\n          }\n        }\n\n        this.prevChunkNames = chunkNames\n      }\n    )\n\n    this.webpackHotMiddleware = new WebpackHotMiddleware(\n      this.multiCompiler.compilers,\n      this.versionInfo,\n      this.devtoolsFrontendUrl\n    )\n\n    let booted = false\n\n    this.watcher = await new Promise((resolve) => {\n      const watcher = this.multiCompiler?.watch(\n        // @ts-ignore webpack supports an array of watchOptions when using a multiCompiler\n        this.activeWebpackConfigs.map((config) => config.watchOptions!),\n        // Errors are handled separately\n        (_err: any) => {\n          if (!booted) {\n            booted = true\n            resolve(watcher)\n          }\n        }\n      )\n    })\n\n    this.onDemandEntries = onDemandEntryHandler({\n      hotReloader: this,\n      multiCompiler: this.multiCompiler,\n      pagesDir: this.pagesDir,\n      appDir: this.appDir,\n      rootDir: this.dir,\n      nextConfig: this.config,\n      ...(this.config.onDemandEntries as {\n        maxInactiveAge: number\n        pagesBufferLength: number\n      }),\n    })\n\n    this.middlewares = [\n      getOverlayMiddleware({\n        rootDirectory: this.dir,\n        clientStats: () => this.clientStats,\n        serverStats: () => this.serverStats,\n        edgeServerStats: () => this.edgeServerStats,\n      }),\n      getSourceMapMiddleware({\n        clientStats: () => this.clientStats,\n        serverStats: () => this.serverStats,\n        edgeServerStats: () => this.edgeServerStats,\n      }),\n      getNextErrorFeedbackMiddleware(this.telemetry),\n      getDevOverlayFontMiddleware(),\n      getDisableDevIndicatorMiddleware(),\n    ]\n  }\n\n  public invalidate(\n    { reloadAfterInvalidation }: { reloadAfterInvalidation: boolean } = {\n      reloadAfterInvalidation: false,\n    }\n  ) {\n    // Cache the `reloadAfterInvalidation` flag, and use it to reload the page when compilation is done\n    this.reloadAfterInvalidation = reloadAfterInvalidation\n    const outputPath = this.multiCompiler?.outputPath\n    if (outputPath) {\n      getInvalidator(outputPath)?.invalidate()\n    }\n  }\n\n  public async getCompilationErrors(page: string) {\n    const getErrors = ({ compilation }: webpack.Stats) => {\n      const failedPages = erroredPages(compilation)\n      const normalizedPage = normalizePathSep(page)\n      // If there is an error related to the requesting page we display it instead of the first error\n      return failedPages[normalizedPage]?.length > 0\n        ? failedPages[normalizedPage]\n        : compilation.errors\n    }\n\n    if (this.clientError) {\n      return [this.clientError]\n    } else if (this.serverError) {\n      return [this.serverError]\n    } else if (this.clientStats?.hasErrors()) {\n      return getErrors(this.clientStats)\n    } else if (this.serverStats?.hasErrors()) {\n      return getErrors(this.serverStats)\n    } else if (this.edgeServerStats?.hasErrors()) {\n      return getErrors(this.edgeServerStats)\n    } else {\n      return []\n    }\n  }\n\n  public send(action: HMR_ACTION_TYPES): void {\n    this.webpackHotMiddleware!.publish(action)\n  }\n\n  public async ensurePage({\n    page,\n    clientOnly,\n    appPaths,\n    definition,\n    isApp,\n    url,\n  }: {\n    page: string\n    clientOnly: boolean\n    appPaths?: ReadonlyArray<string> | null\n    isApp?: boolean\n    definition?: RouteDefinition\n    url?: string\n  }): Promise<void> {\n    return this.hotReloaderSpan\n      .traceChild('ensure-page', {\n        inputPage: page,\n      })\n      .traceAsyncFn(async () => {\n        // Make sure we don't re-build or dispose prebuilt pages\n        if (page !== '/_error' && BLOCKED_PAGES.indexOf(page) !== -1) {\n          return\n        }\n        const error = clientOnly\n          ? this.clientError\n          : this.serverError || this.clientError\n        if (error) {\n          throw error\n        }\n\n        return this.onDemandEntries?.ensurePage({\n          page,\n          appPaths,\n          definition,\n          isApp,\n          url,\n        })\n      })\n  }\n\n  public close() {\n    this.webpackHotMiddleware?.close()\n  }\n}\n"], "names": ["webpack", "StringXor", "getOverlayMiddleware", "getSourceMapMiddleware", "WebpackHotMiddleware", "join", "relative", "isAbsolute", "posix", "createEntrypoints", "createPagesMapping", "finalizeEntrypoint", "getClientEntry", "getEdgeServerEntry", "getAppEntry", "runDependingOnPageType", "getStaticInfoIncludingLayouts", "getInstrumentationEntry", "watchCompilers", "Log", "getBaseWebpackConfig", "loadProjectInfo", "APP_DIR_ALIAS", "WEBPACK_LAYERS", "recursiveDelete", "BLOCKED_PAGES", "CLIENT_STATIC_FILES_RUNTIME_AMP", "CLIENT_STATIC_FILES_RUNTIME_MAIN", "CLIENT_STATIC_FILES_RUNTIME_MAIN_APP", "CLIENT_STATIC_FILES_RUNTIME_REACT_REFRESH", "COMPILER_NAMES", "RSC_MODULE_TYPES", "getPathMatch", "findPageFile", "BUILDING", "getEntries", "EntryTypes", "getInvalidator", "onDemandEntryHandler", "denormalizePagePath", "normalizePathSep", "getRouteFromEntrypoint", "difference", "isInstrumentationHookFile", "isMiddlewareFile", "isMiddlewareFilename", "DecodeError", "trace", "getProperError", "ws", "existsSync", "promises", "fs", "parseVersionInfo", "isAPIRoute", "getRouteLoaderEntry", "isInternalComponent", "isNonRoutePagesPage", "RouteKind", "HMR_ACTIONS_SENT_TO_BROWSER", "PAGE_TYPES", "FAST_REFRESH_RUNTIME_RELOAD", "getNodeDebugType", "getNextErrorFeedbackMiddleware", "getDevOverlayFontMiddleware", "getDisableDevIndicatorMiddleware", "MILLISECONDS_IN_NANOSECOND", "BigInt", "diff", "a", "b", "Set", "filter", "v", "has", "wsServer", "Server", "noServer", "renderScriptError", "res", "error", "verbose", "<PERSON><PERSON><PERSON><PERSON>", "code", "finished", "undefined", "console", "stack", "statusCode", "end", "addCorsSupport", "req", "url", "startsWith", "preflight", "headers", "origin", "method", "writeHead", "matchNextPageBundleRequest", "findEntryModule", "module", "compilation", "issuer", "moduleGraph", "get<PERSON><PERSON><PERSON>", "erroredPages", "failedPages", "errors", "entryModule", "name", "enhancedName", "push", "getVersionInfo", "installed", "require", "version", "fetch", "ok", "staleness", "latest", "canary", "json", "e", "HotReloaderWebpack", "constructor", "dir", "config", "pagesDir", "distDir", "buildId", "<PERSON><PERSON><PERSON>", "previewProps", "rewrites", "appDir", "telemetry", "resetFetch", "clientError", "serverError", "hmrServerError", "pagesMapping", "versionInfo", "reloadAfterInvalidation", "hasAmpEntrypoints", "hasAppRouterEntrypoints", "hasPagesRouterEntrypoints", "middlewares", "clientStats", "serverStats", "edgeServerStats", "serverPrevDocumentHash", "hotReloaderSpan", "process", "env", "__NEXT_VERSION", "stop", "run", "parsedUrl", "handlePageBundleRequest", "pageBundleRes", "parsedPageBundleUrl", "pathname", "params", "decodedPagePath", "path", "map", "param", "decodeURIComponent", "_", "page", "indexOf", "ensurePage", "clientOnly", "getCompilationErrors", "length", "middleware", "calledNext", "setHmrServerError", "clearHmrServerError", "send", "action", "RELOAD_PAGE", "data", "refreshServerComponents", "hash", "SERVER_COMPONENT_CHANGES", "onHMR", "_socket", "head", "callback", "handleUpgrade", "socket", "client", "webpackHotMiddleware", "onDemandEntries", "addEventListener", "toString", "payload", "JSON", "parse", "<PERSON><PERSON><PERSON><PERSON>", "event", "spanName", "startTime", "Math", "floor", "attrs", "attributes", "endTime", "updatedModules", "m", "replace", "appPagesBrowser", "isPageHidden", "errorCount", "warningCount", "stackTrace", "hadRuntimeError", "warn", "fileMessage", "file", "exec", "fileUrl", "URL", "cwd", "modules", "searchParams", "getAll", "filepath", "slice", "pagesDirBrowser", "cleanedFile<PERSON>ath", "manualTraceChild", "clientId", "id", "clean", "span", "traceAsyncFn", "getWebpackConfig", "webpackConfigSpan", "pageExtensions", "pagePaths", "Promise", "all", "isDev", "pagesType", "PAGES", "i", "entrypoints", "envFiles", "pages", "previewMode", "rootDir", "commonWebpackOptions", "dev", "originalRewrites", "_originalRewrites", "originalRedirects", "_originalRedirects", "runWebpackSpan", "info", "compilerType", "server", "edgeServer", "buildFallbackError", "fallback<PERSON><PERSON><PERSON>", "fallbackConfig", "beforeFiles", "afterFiles", "fallback", "isDev<PERSON><PERSON><PERSON>", "fallbackCompiler", "resolve", "bootedFallbackCompiler", "watch", "watchOptions", "_err", "tracedGetVersionInfo", "versionInfoSpan", "start", "startSpan", "nodeDebugType", "devtoolsFrontendUrl", "debugPort", "debugInfo", "debugInfoList", "then", "mkdir", "recursive", "distPackageJsonPath", "writeFile", "activeWebpackConfigs", "defaultEntry", "entry", "args", "outputPath", "multiCompiler", "entries", "isClientCompilation", "isNodeServerCompilation", "isEdgeServerCompilation", "Object", "keys", "<PERSON><PERSON><PERSON>", "entryData", "bundlePath", "dispose", "result", "key", "isEntry", "type", "ENTRY", "isChildEntry", "CHILD_ENTRY", "pageExists", "absolutePagePath", "absoluteEntryFilePath", "hasAppDir", "isAppPath", "staticInfo", "isInsideAppDir", "pageFilePath", "amp", "isServerComponent", "rsc", "pageType", "APP", "ROOT", "isInstrumentation", "pageRuntime", "runtime", "experimental", "nodeMiddleware", "onEdgeServer", "status", "normalizedBundlePath", "value", "isEdgeServer", "appDirLoader", "appPaths", "pagePath", "tsconfigPath", "typescript", "basePath", "assetPrefix", "nextConfigOutput", "output", "preferredRegion", "middlewareConfig", "<PERSON><PERSON><PERSON>", "from", "stringify", "import", "onClient", "request", "onServer", "relativeRequest", "context", "kind", "PAGES_API", "parallelism", "inputFileSystem", "compilers", "compiler", "fsStartTime", "Date", "now", "hooks", "beforeRun", "intercept", "register", "tapInfo", "done", "tap", "purge", "changedClientPages", "changedServerPages", "changedEdgeServerPages", "changedServerComponentPages", "changedCSSImportPages", "prevClientPageHashes", "Map", "prevServerPageHashes", "prevEdgeServerPageHashes", "prevCSSImportModuleHashes", "pageExtensionRegex", "RegExp", "trackPageChanges", "pageHashMap", "changedItems", "serverComponentChangedItems", "stats", "for<PERSON>ach", "chunks", "chunk", "modsIterable", "chunkGraph", "getChunkModulesIterable", "hasCSSModuleChanges", "chunksHash", "chunksHashServerLayer", "mod", "resource", "includes", "test", "createHash", "update", "originalSource", "buffer", "digest", "layer", "reactServerComponents", "buildInfo", "add", "getModuleHash", "resourceKey", "prevHash", "get", "set", "curHash", "server<PERSON>ey", "prevServerHash", "curServerHash", "err", "emit", "failed", "serverChunkNames", "documentChunk", "namedChunks", "chunkNames", "diffChunkNames", "every", "chunkName", "serverOnlyChanges", "edgeServerOnlyChanges", "pageChanges", "concat", "middlewareChanges", "Array", "MIDDLEWARE_CHANGES", "SERVER_ONLY_CHANGES", "pg", "size", "clear", "prevChunkNames", "addedPages", "removedPages", "addedPage", "ADDED_PAGE", "removedPage", "REMOVED_PAGE", "booted", "watcher", "hotReloader", "nextConfig", "rootDirectory", "invalidate", "getErrors", "normalizedPage", "hasErrors", "publish", "definition", "isApp", "inputPage", "close"], "mappings": "AAQA,SAASA,OAAO,EAAEC,SAAS,QAAQ,qCAAoC;AACvE,SACEC,oBAAoB,EACpBC,sBAAsB,QACjB,sEAAqE;AAC5E,SAASC,oBAAoB,QAAQ,mBAAkB;AACvD,SAASC,IAAI,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,KAAK,QAAQ,OAAM;AACxD,SACEC,iBAAiB,EACjBC,kBAAkB,EAClBC,kBAAkB,EAClBC,cAAc,EACdC,kBAAkB,EAClBC,WAAW,EACXC,sBAAsB,EACtBC,6BAA6B,EAC7BC,uBAAuB,QAClB,sBAAqB;AAC5B,SAASC,cAAc,QAAQ,qBAAoB;AACnD,YAAYC,SAAS,yBAAwB;AAC7C,OAAOC,wBACLC,eAAe,QACV,6BAA4B;AACnC,SAASC,aAAa,EAAEC,cAAc,QAAQ,sBAAqB;AACnE,SAASC,eAAe,QAAQ,6BAA4B;AAC5D,SACEC,aAAa,EACbC,+BAA+B,EAC/BC,gCAAgC,EAChCC,oCAAoC,EACpCC,yCAAyC,EACzCC,cAAc,EACdC,gBAAgB,QACX,6BAA4B;AAEnC,SAASC,YAAY,QAAQ,2CAA0C;AACvE,SAASC,YAAY,QAAQ,wBAAuB;AACpD,SACEC,QAAQ,EACRC,UAAU,EACVC,UAAU,EACVC,cAAc,EACdC,oBAAoB,QACf,4BAA2B;AAClC,SAASC,mBAAmB,QAAQ,mDAAkD;AACtF,SAASC,gBAAgB,QAAQ,gDAA+C;AAChF,OAAOC,4BAA4B,+BAA8B;AACjE,SACEC,UAAU,EACVC,yBAAyB,EACzBC,gBAAgB,EAChBC,oBAAoB,QACf,oBAAmB;AAC1B,SAASC,WAAW,QAAQ,yBAAwB;AACpD,SAAoBC,KAAK,QAAQ,cAAa;AAC9C,SAASC,cAAc,QAAQ,qBAAoB;AACnD,OAAOC,QAAQ,wBAAuB;AACtC,SAASC,UAAU,EAAEC,YAAYC,EAAE,QAAQ,KAAI;AAE/C,SAASC,gBAAgB,QAAQ,uBAAsB;AAEvD,SAASC,UAAU,QAAQ,yBAAwB;AACnD,SAASC,mBAAmB,QAAQ,gDAA+C;AACnF,SACEC,mBAAmB,EACnBC,mBAAmB,QACd,kCAAiC;AACxC,SAASC,SAAS,QAAQ,gBAAe;AACzC,SACEC,2BAA2B,QAEtB,uBAAsB;AAG7B,SAASC,UAAU,QAAQ,uBAAsB;AACjD,SAASC,2BAA2B,QAAQ,aAAY;AACxD,SAASC,gBAAgB,QAAQ,eAAc;AAC/C,SAASC,8BAA8B,QAAQ,sFAAqF;AACpI,SAASC,2BAA2B,QAAQ,iFAAgF;AAC5H,SAASC,gCAAgC,QAAQ,6BAA4B;AAE7E,MAAMC,6BAA6BC,OAAO;AAE1C,SAASC,KAAKC,CAAW,EAAEC,CAAW;IACpC,OAAO,IAAIC,IAAI;WAAIF;KAAE,CAACG,MAAM,CAAC,CAACC,IAAM,CAACH,EAAEI,GAAG,CAACD;AAC7C;AAEA,MAAME,WAAW,IAAI1B,GAAG2B,MAAM,CAAC;IAAEC,UAAU;AAAK;AAEhD,OAAO,eAAeC,kBACpBC,GAAmB,EACnBC,KAAY,EACZ,EAAEC,UAAU,IAAI,EAAE,GAAG,CAAC,CAAC;IAEvB,wDAAwD;IACxDF,IAAIG,SAAS,CACX,iBACA;IAGF,IAAI,AAACF,MAAcG,IAAI,KAAK,UAAU;QACpC,OAAO;YAAEC,UAAUC;QAAU;IAC/B;IAEA,IAAIJ,SAAS;QACXK,QAAQN,KAAK,CAACA,MAAMO,KAAK;IAC3B;IACAR,IAAIS,UAAU,GAAG;IACjBT,IAAIU,GAAG,CAAC;IACR,OAAO;QAAEL,UAAU;IAAK;AAC1B;AAEA,SAASM,eAAeC,GAAoB,EAAEZ,GAAmB;IAC/D,wEAAwE;IACxE,IAAI,CAACY,IAAIC,GAAG,CAAEC,UAAU,CAAC,YAAY;QACnC,OAAO;YAAEC,WAAW;QAAM;IAC5B;IAEA,IAAI,CAACH,IAAII,OAAO,CAACC,MAAM,EAAE;QACvB,OAAO;YAAEF,WAAW;QAAM;IAC5B;IAEAf,IAAIG,SAAS,CAAC,+BAA+BS,IAAII,OAAO,CAACC,MAAM;IAC/DjB,IAAIG,SAAS,CAAC,gCAAgC;IAC9C,gHAAgH;IAChH,IAAIS,IAAII,OAAO,CAAC,iCAAiC,EAAE;QACjDhB,IAAIG,SAAS,CACX,gCACAS,IAAII,OAAO,CAAC,iCAAiC;IAEjD;IAEA,IAAIJ,IAAIM,MAAM,KAAK,WAAW;QAC5BlB,IAAImB,SAAS,CAAC;QACdnB,IAAIU,GAAG;QACP,OAAO;YAAEK,WAAW;QAAK;IAC3B;IAEA,OAAO;QAAEA,WAAW;IAAM;AAC5B;AAEA,OAAO,MAAMK,6BAA6BnE,aACxC,iDACD;AAED,6DAA6D;AAC7D,SAASoE,gBACPC,MAAsB,EACtBC,WAAgC;IAEhC,OAAS;QACP,MAAMC,SAASD,YAAYE,WAAW,CAACC,SAAS,CAACJ;QACjD,IAAI,CAACE,QAAQ,OAAOF;QACpBA,SAASE;IACX;AACF;AAEA,SAASG,aAAaJ,WAAgC;IACpD,MAAMK,cAAkD,CAAC;IACzD,KAAK,MAAM3B,SAASsB,YAAYM,MAAM,CAAE;QACtC,IAAI,CAAC5B,MAAMqB,MAAM,EAAE;YACjB;QACF;QAEA,MAAMQ,cAAcT,gBAAgBpB,MAAMqB,MAAM,EAAEC;QAClD,MAAM,EAAEQ,IAAI,EAAE,GAAGD;QACjB,IAAI,CAACC,MAAM;YACT;QACF;QAEA,iCAAiC;QACjC,MAAMC,eAAetE,uBAAuBqE;QAE5C,IAAI,CAACC,cAAc;YACjB;QACF;QAEA,IAAI,CAACJ,WAAW,CAACI,aAAa,EAAE;YAC9BJ,WAAW,CAACI,aAAa,GAAG,EAAE;QAChC;QAEAJ,WAAW,CAACI,aAAa,CAACC,IAAI,CAAChC;IACjC;IAEA,OAAO2B;AACT;AAEA,OAAO,eAAeM;IACpB,IAAIC,YAAY;IAEhB,IAAI;QACFA,YAAYC,QAAQ,qBAAqBC,OAAO;QAEhD,IAAIrC;QAEJ,IAAI;YACF,8CAA8C;YAC9CA,MAAM,MAAMsC,MAAM;QACpB,EAAE,OAAM;QACN,sBAAsB;QACxB;QAEA,IAAI,CAACtC,OAAO,CAACA,IAAIuC,EAAE,EAAE,OAAO;YAAEJ;YAAWK,WAAW;QAAU;QAE9D,MAAM,EAAEC,MAAM,EAAEC,MAAM,EAAE,GAAG,MAAM1C,IAAI2C,IAAI;QAEzC,OAAOrE,iBAAiB;YAAE6D;YAAWM;YAAQC;QAAO;IACtD,EAAE,OAAOE,GAAQ;QACfrC,QAAQN,KAAK,CAAC2C;QACd,OAAO;YAAET;YAAWK,WAAW;QAAU;IAC3C;AACF;AAEA,eAAe,MAAMK;IA+CnBC,YACEC,GAAW,EACX,EACEC,MAAM,EACNC,QAAQ,EACRC,OAAO,EACPC,OAAO,EACPC,aAAa,EACbC,YAAY,EACZC,QAAQ,EACRC,MAAM,EACNC,SAAS,EACTC,UAAU,EAYX,CACD;aAvDMC,cAA4B;aAC5BC,cAA4B;aAC5BC,iBAA+B;aAU/BC,eAA0C,CAAC;aAI3CC,cAA2B;YACjCtB,WAAW;YACXL,WAAW;QACb;aAEQ4B,0BAAmC;QAmCzC,IAAI,CAACC,iBAAiB,GAAG;QACzB,IAAI,CAACC,uBAAuB,GAAG;QAC/B,IAAI,CAACC,yBAAyB,GAAG;QACjC,IAAI,CAACf,OAAO,GAAGA;QACf,IAAI,CAACC,aAAa,GAAGA;QACrB,IAAI,CAACL,GAAG,GAAGA;QACX,IAAI,CAACoB,WAAW,GAAG,EAAE;QACrB,IAAI,CAAClB,QAAQ,GAAGA;QAChB,IAAI,CAACM,MAAM,GAAGA;QACd,IAAI,CAACL,OAAO,GAAGA;QACf,IAAI,CAACkB,WAAW,GAAG;QACnB,IAAI,CAACC,WAAW,GAAG;QACnB,IAAI,CAACC,eAAe,GAAG;QACvB,IAAI,CAACC,sBAAsB,GAAG;QAC9B,IAAI,CAACf,SAAS,GAAGA;QACjB,IAAI,CAACC,UAAU,GAAGA;QAElB,IAAI,CAACT,MAAM,GAAGA;QACd,IAAI,CAACK,YAAY,GAAGA;QACpB,IAAI,CAACC,QAAQ,GAAGA;QAChB,IAAI,CAACkB,eAAe,GAAGxG,MAAM,gBAAgBsC,WAAW;YACtD+B,SAASoC,QAAQC,GAAG,CAACC,cAAc;QACrC;QACA,8FAA8F;QAC9F,wCAAwC;QACxC,IAAI,CAACH,eAAe,CAACI,IAAI;IAC3B;IAEA,MAAaC,IACXjE,GAAoB,EACpBZ,GAAmB,EACnB8E,SAAoB,EACU;QAC9B,qFAAqF;QACrF,iEAAiE;QACjE,iEAAiE;QACjE,0CAA0C;QAC1C,MAAM,EAAE/D,SAAS,EAAE,GAAGJ,eAAeC,KAAKZ;QAC1C,IAAIe,WAAW;YACb,OAAO,CAAC;QACV;QAEA,6FAA6F;QAC7F,8FAA8F;QAC9F,kEAAkE;QAClE,uFAAuF;QACvF,MAAMgE,0BAA0B,OAC9BC,eACAC;YAEA,MAAM,EAAEC,QAAQ,EAAE,GAAGD;YACrB,IAAI,CAACC,UAAU,OAAO,CAAC;YAEvB,MAAMC,SAAS/D,2BAA2B8D;YAC1C,IAAI,CAACC,QAAQ,OAAO,CAAC;YAErB,IAAIC;YAEJ,IAAI;gBACFA,kBAAkB,CAAC,CAAC,EAAED,OAAOE,IAAI,CAC9BC,GAAG,CAAC,CAACC,QAAkBC,mBAAmBD,QAC1CjK,IAAI,CAAC,MAAM;YAChB,EAAE,OAAOmK,GAAG;gBACV,MAAM,qBAAyC,CAAzC,IAAI1H,YAAY,2BAAhB,qBAAA;2BAAA;gCAAA;kCAAA;gBAAwC;YAChD;YAEA,MAAM2H,OAAOlI,oBAAoB4H;YAEjC,IAAIM,SAAS,aAAahJ,cAAciJ,OAAO,CAACD,UAAU,CAAC,GAAG;gBAC5D,IAAI;oBACF,MAAM,IAAI,CAACE,UAAU,CAAC;wBAAEF;wBAAMG,YAAY;wBAAMhF,KAAKD,IAAIC,GAAG;oBAAC;gBAC/D,EAAE,OAAOZ,OAAO;oBACd,OAAO,MAAMF,kBAAkBiF,eAAe/G,eAAegC;gBAC/D;gBAEA,MAAM4B,SAAS,MAAM,IAAI,CAACiE,oBAAoB,CAACJ;gBAC/C,IAAI7D,OAAOkE,MAAM,GAAG,GAAG;oBACrB,OAAO,MAAMhG,kBAAkBiF,eAAenD,MAAM,CAAC,EAAE,EAAE;wBACvD3B,SAAS;oBACX;gBACF;YACF;YAEA,OAAO,CAAC;QACV;QAEA,MAAM,EAAEG,QAAQ,EAAE,GAAG,MAAM0E,wBAAwB/E,KAAK8E;QAExD,KAAK,MAAMkB,cAAc,IAAI,CAAC7B,WAAW,CAAE;YACzC,IAAI8B,aAAa;YAEjB,MAAMD,WAAWpF,KAAKZ,KAAK;gBACzBiG,aAAa;YACf;YAEA,IAAI,CAACA,YAAY;gBACf,OAAO;oBAAE5F,UAAU;gBAAK;YAC1B;QACF;QAEA,OAAO;YAAEA;QAAS;IACpB;IAEO6F,kBAAkBjG,KAAmB,EAAQ;QAClD,IAAI,CAAC2D,cAAc,GAAG3D;IACxB;IAEOkG,sBAA4B;QACjC,IAAI,IAAI,CAACvC,cAAc,EAAE;YACvB,IAAI,CAACsC,iBAAiB,CAAC;YACvB,IAAI,CAACE,IAAI,CAAC;gBACRC,QAAQzH,4BAA4B0H,WAAW;gBAC/CC,MAAM;YACR;QACF;IACF;IAEA,MAAgBC,wBAAwBC,IAAY,EAAiB;QACnE,IAAI,CAACL,IAAI,CAAC;YACRC,QAAQzH,4BAA4B8H,wBAAwB;YAC5DD;QAGF;IACF;IAEOE,MACL/F,GAAoB,EACpBgG,OAAe,EACfC,IAAY,EACZC,QAAwC,EACxC;QACAlH,SAASmH,aAAa,CAACnG,KAAKA,IAAIoG,MAAM,EAAEH,MAAM,CAACI;gBAC7C,4BACA;aADA,6BAAA,IAAI,CAACC,oBAAoB,qBAAzB,2BAA2BP,KAAK,CAACM;aACjC,wBAAA,IAAI,CAACE,eAAe,qBAApB,sBAAsBR,KAAK,CAACM,QAAQ,IAAM,IAAI,CAACrD,cAAc;YAC7DkD,SAASG;YAETA,OAAOG,gBAAgB,CAAC,WAAW,CAAC,EAAEb,IAAI,EAAE;gBAC1CA,OAAO,OAAOA,SAAS,WAAWA,KAAKc,QAAQ,KAAKd;gBAEpD,IAAI;oBACF,MAAMe,UAAUC,KAAKC,KAAK,CAACjB;oBAE3B,IAAIkB;oBASJ,OAAQH,QAAQI,KAAK;wBACnB,KAAK;4BAAY;gCACfD,aAAa;oCACX1F,MAAMuF,QAAQK,QAAQ;oCACtBC,WACExI,OAAOyI,KAAKC,KAAK,CAACR,QAAQM,SAAS,KACnCzI;oCACF4I,OAAOT,QAAQU,UAAU;oCACzBC,SACE7I,OAAOyI,KAAKC,KAAK,CAACR,QAAQW,OAAO,KACjC9I;gCACJ;gCACA;4BACF;wBACA,KAAK;4BAAsB;gCACzBsI,aAAa;oCACX1F,MAAMuF,QAAQI,KAAK;oCACnBE,WACExI,OAAOkI,QAAQM,SAAS,IAAIzI;oCAC9B8I,SAAS7I,OAAOkI,QAAQW,OAAO,IAAI9I;oCACnC4I,OAAO;wCACLG,gBAAgBZ,QAAQY,cAAc,CAAC5C,GAAG,CAAC,CAAC6C,IAC1CA,EACGC,OAAO,CAAC,CAAC,CAAC,EAAE5L,eAAe6L,eAAe,CAAC,EAAE,CAAC,EAAE,IAChDD,OAAO,CAAC,SAAS;wCAEtB1C,MAAM4B,QAAQ5B,IAAI;wCAClB4C,cAAchB,QAAQgB,YAAY;oCACpC;gCACF;gCACA;4BACF;wBACA,KAAK;wBACL,KAAK;4BAAkB;gCACrBb,aAAa;oCACX1F,MAAMuF,QAAQI,KAAK;gCACrB;gCACA;4BACF;wBACA,KAAK;4BAAgB;gCACnBD,aAAa;oCACX1F,MAAMuF,QAAQI,KAAK;oCACnBK,OAAO;wCAAEQ,YAAYjB,QAAQiB,UAAU;oCAAC;gCAC1C;gCACA;4BACF;wBACA,KAAK;4BAAkB;gCACrBd,aAAa;oCACX1F,MAAMuF,QAAQI,KAAK;oCACnBK,OAAO;wCAAES,cAAclB,QAAQkB,YAAY;oCAAC;gCAC9C;gCACA;4BACF;wBACA,KAAK;wBACL,KAAK;4BAAqB;gCACxBf,aAAa;oCACX1F,MAAMuF,QAAQI,KAAK;oCACnBK,OAAO;wCAAErC,MAAM4B,QAAQ5B,IAAI,IAAI;oCAAG;gCACpC;gCACA;4BACF;wBACA,KAAK;4BAAsB;gCACzB,MAAM,EAAEgC,KAAK,EAAEe,UAAU,EAAEC,eAAe,EAAE,GAAGpB;gCAE/CG,aAAa;oCACX1F,MAAM2F;oCACNK,OAAO;wCAAEU,YAAYA,cAAc;oCAAG;gCACxC;gCAEA,IAAIC,iBAAiB;oCACnBtM,IAAIuM,IAAI,CAAC7J;oCACT;gCACF;gCAEA,IAAI8J,cAAc;gCAClB,IAAIH,YAAY;wCACD;oCAAb,MAAMI,QAAO,QAAA,uCAAuCC,IAAI,CACtDL,gCADW,KAEV,CAAC,EAAE;oCACN,IAAII,MAAM;wCACR,8DAA8D;wCAC9D,oEAAoE;wCACpE,IAAIA,KAAK/H,UAAU,CAAC,CAAC,CAAC,EAAEtE,eAAe6L,eAAe,CAAC,EAAE,CAAC,GAAG;4CAC3D,MAAMU,UAAU,IAAIC,IAAIH,MAAM;4CAC9B,MAAMI,MAAMxE,QAAQwE,GAAG;4CACvB,MAAMC,UAAUH,QAAQI,YAAY,CACjCC,MAAM,CAAC,WACP9D,GAAG,CAAC,CAAC+D,WAAaA,SAASC,KAAK,CAACL,IAAIlD,MAAM,GAAG,IAC9CtG,MAAM,CACL,CAAC4J,WAAa,CAACA,SAASvI,UAAU,CAAC;4CAGvC,IAAIoI,QAAQnD,MAAM,GAAG,GAAG;gDACtB6C,cAAc,CAAC,MAAM,EAAEM,QAAQ5N,IAAI,CAAC,MAAM,QAAQ,CAAC;4CACrD;wCACF,OAAO,IACL,8BAA8B;wCAC9BuN,KAAK/H,UAAU,CAAC,CAAC,CAAC,EAAEtE,eAAe+M,eAAe,CAAC,EAAE,CAAC,GACtD;4CACA,MAAMC,kBAAkBX,KAAKS,KAAK,CAChC,CAAC,CAAC,EAAE9M,eAAe+M,eAAe,CAAC,EAAE,CAAC,CAACxD,MAAM;4CAG/C6C,cAAc,CAAC,MAAM,EAAEY,gBAAgB,QAAQ,CAAC;wCAClD,OAAO;4CACLZ,cAAc,CAAC,MAAM,EAAEC,KAAK,QAAQ,CAAC;wCACvC;oCACF;gCACF;gCAEAzM,IAAIuM,IAAI,CACN,CAAC,yCAAyC,EAAEC,YAAY,iEAAiE,CAAC;gCAE5H;4BACF;wBACA;4BAAS;gCACP;4BACF;oBACF;oBAEA,IAAInB,YAAY;wBACd,IAAI,CAACjD,eAAe,CAACiF,gBAAgB,CACnChC,WAAW1F,IAAI,EACf0F,WAAWG,SAAS,EACpBH,WAAWQ,OAAO,EAClB;4BAAE,GAAGR,WAAWM,KAAK;4BAAE2B,UAAUpC,QAAQqC,EAAE;wBAAC;oBAEhD;gBACF,EAAE,OAAOlE,GAAG;gBACV,4BAA4B;gBAC9B;YACF;QACF;IACF;IAEA,MAAcmE,MAAMC,IAAU,EAAiB;QAC7C,OAAOA,KACJpC,UAAU,CAAC,SACXqC,YAAY,CAAC,IACZrN,gBAAgBnB,KAAK,IAAI,CAACyH,GAAG,EAAE,IAAI,CAACC,MAAM,CAACE,OAAO,GAAG;IAE3D;IAEA,MAAc6G,iBAAiBF,IAAU,EAAE;QACzC,MAAMG,oBAAoBH,KAAKpC,UAAU,CAAC;QAE1C,MAAMwC,iBAAiB,IAAI,CAACjH,MAAM,CAACiH,cAAc;QAEjD,OAAOD,kBAAkBF,YAAY,CAAC;YACpC,MAAMI,YAAY,CAAC,IAAI,CAACjH,QAAQ,GAC3B,EAAE,GACH,MAAM+G,kBACHvC,UAAU,CAAC,kBACXqC,YAAY,CAAC,IACZK,QAAQC,GAAG,CAAC;oBACVlN,aAAa,IAAI,CAAC+F,QAAQ,EAAG,SAASgH,gBAAgB;oBACtD/M,aACE,IAAI,CAAC+F,QAAQ,EACb,cACAgH,gBACA;iBAEH;YAGT,IAAI,CAACpG,YAAY,GAAG,MAAMmG,kBACvBvC,UAAU,CAAC,wBACXqC,YAAY,CAAC,IACZnO,mBAAmB;oBACjB0O,OAAO;oBACPJ,gBAAgB,IAAI,CAACjH,MAAM,CAACiH,cAAc;oBAC1CK,WAAWzL,WAAW0L,KAAK;oBAC3BL,WAAWA,UAAUzK,MAAM,CACzB,CAAC+K,IAAkC,OAAOA,MAAM;oBAElDvH,UAAU,IAAI,CAACA,QAAQ;oBACvBM,QAAQ,IAAI,CAACA,MAAM;gBACrB;YAGJ,MAAMkH,cAAc,MAAMT,kBACvBvC,UAAU,CAAC,sBACXqC,YAAY,CAAC,IACZpO,kBAAkB;oBAChB6H,QAAQ,IAAI,CAACA,MAAM;oBACnBJ,SAAS,IAAI,CAACA,OAAO;oBACrBH,QAAQ,IAAI,CAACA,MAAM;oBACnB0H,UAAU,EAAE;oBACZL,OAAO;oBACPM,OAAO,IAAI,CAAC9G,YAAY;oBACxBZ,UAAU,IAAI,CAACA,QAAQ;oBACvB2H,aAAa,IAAI,CAACvH,YAAY;oBAC9BwH,SAAS,IAAI,CAAC9H,GAAG;oBACjBkH,gBAAgB,IAAI,CAACjH,MAAM,CAACiH,cAAc;gBAC5C;YAGJ,MAAMa,uBAAuB;gBAC3BC,KAAK;gBACL5H,SAAS,IAAI,CAACA,OAAO;gBACrBC,eAAe,IAAI,CAACA,aAAa;gBACjCJ,QAAQ,IAAI,CAACA,MAAM;gBACnBC,UAAU,IAAI,CAACA,QAAQ;gBACvBK,UAAU,IAAI,CAACA,QAAQ;gBACvB0H,kBAAkB,IAAI,CAAChI,MAAM,CAACiI,iBAAiB;gBAC/CC,mBAAmB,IAAI,CAAClI,MAAM,CAACmI,kBAAkB;gBACjDC,gBAAgB,IAAI,CAAC5G,eAAe;gBACpCjB,QAAQ,IAAI,CAACA,MAAM;YACrB;YAEA,OAAOyG,kBACJvC,UAAU,CAAC,2BACXqC,YAAY,CAAC;gBACZ,MAAMuB,OAAO,MAAM/O,gBAAgB;oBACjCyG,KAAK,IAAI,CAACA,GAAG;oBACbC,QAAQ8H,qBAAqB9H,MAAM;oBACnC+H,KAAK;gBACP;gBACA,OAAOZ,QAAQC,GAAG,CAAC;oBACjB,0BAA0B;oBAC1B/N,qBAAqB,IAAI,CAAC0G,GAAG,EAAE;wBAC7B,GAAG+H,oBAAoB;wBACvBQ,cAAcvO,eAAekK,MAAM;wBACnCwD,aAAaA,YAAYxD,MAAM;wBAC/B,GAAGoE,IAAI;oBACT;oBACAhP,qBAAqB,IAAI,CAAC0G,GAAG,EAAE;wBAC7B,GAAG+H,oBAAoB;wBACvBQ,cAAcvO,eAAewO,MAAM;wBACnCd,aAAaA,YAAYc,MAAM;wBAC/B,GAAGF,IAAI;oBACT;oBACAhP,qBAAqB,IAAI,CAAC0G,GAAG,EAAE;wBAC7B,GAAG+H,oBAAoB;wBACvBQ,cAAcvO,eAAeyO,UAAU;wBACvCf,aAAaA,YAAYe,UAAU;wBACnC,GAAGH,IAAI;oBACT;iBACD;YACH;QACJ;IACF;IAEA,MAAaI,qBAAoC;QAC/C,IAAI,IAAI,CAACC,eAAe,EAAE;QAE1B,MAAML,OAAO,MAAM/O,gBAAgB;YACjCyG,KAAK,IAAI,CAACA,GAAG;YACbC,QAAQ,IAAI,CAACA,MAAM;YACnB+H,KAAK;QACP;QACA,MAAMY,iBAAiB,MAAMtP,qBAAqB,IAAI,CAAC0G,GAAG,EAAE;YAC1DqI,gBAAgB,IAAI,CAAC5G,eAAe;YACpCuG,KAAK;YACLO,cAAcvO,eAAekK,MAAM;YACnCjE,QAAQ,IAAI,CAACA,MAAM;YACnBG,SAAS,IAAI,CAACA,OAAO;YACrBC,eAAe,IAAI,CAACA,aAAa;YACjCG,QAAQ,IAAI,CAACA,MAAM;YACnBN,UAAU,IAAI,CAACA,QAAQ;YACvBK,UAAU;gBACRsI,aAAa,EAAE;gBACfC,YAAY,EAAE;gBACdC,UAAU,EAAE;YACd;YACAd,kBAAkB;gBAChBY,aAAa,EAAE;gBACfC,YAAY,EAAE;gBACdC,UAAU,EAAE;YACd;YACAZ,mBAAmB,EAAE;YACrBa,eAAe;YACftB,aAAa,AACX,CAAA,MAAM/O,kBAAkB;gBACtB6H,QAAQ,IAAI,CAACA,MAAM;gBACnBJ,SAAS,IAAI,CAACA,OAAO;gBACrBH,QAAQ,IAAI,CAACA,MAAM;gBACnB0H,UAAU,EAAE;gBACZL,OAAO;gBACPM,OAAO;oBACL,SAAS;oBACT,WAAW;gBACb;gBACA1H,UAAU,IAAI,CAACA,QAAQ;gBACvB2H,aAAa,IAAI,CAACvH,YAAY;gBAC9BwH,SAAS,IAAI,CAAC9H,GAAG;gBACjBkH,gBAAgB,IAAI,CAACjH,MAAM,CAACiH,cAAc;YAC5C,EAAC,EACDhD,MAAM;YACR,GAAGoE,IAAI;QACT;QACA,MAAMW,mBAAmB/Q,QAAQ0Q;QAEjC,IAAI,CAACD,eAAe,GAAG,MAAM,IAAIvB,QAAQ,CAAC8B;YACxC,IAAIC,yBAAyB;YAC7BF,iBAAiBG,KAAK,CACpB,kFAAkF;YAClFR,eAAeS,YAAY,EAC3B,gCAAgC;YAChC,CAACC;gBACC,IAAI,CAACH,wBAAwB;oBAC3BA,yBAAyB;oBACzBD,QAAQ;gBACV;YACF;QAEJ;IACF;IAEA,MAAcK,qBAAqBzC,IAAU,EAAE;QAC7C,MAAM0C,kBAAkB1C,KAAKpC,UAAU,CAAC;QACxC,OAAO8E,gBAAgBzC,YAAY,CAAc,UAC/C5H;IAEJ;IAEA,MAAasK,QAAuB;QAClC,MAAMC,YAAY,IAAI,CAACjI,eAAe,CAACiD,UAAU,CAAC;QAClDgF,UAAU7H,IAAI,GAAG,uDAAuD;;QAExE,IAAI,CAACd,WAAW,GAAG,MAAM,IAAI,CAACwI,oBAAoB,CAACG;QAEnD,MAAMC,gBAAgB3N;QACtB,IAAI2N,iBAAiB,CAAC,IAAI,CAACC,mBAAmB,EAAE;YAC9C,MAAMC,YAAYnI,QAAQmI,SAAS;YACnC,IAAIC;YACJ,IAAI;gBACF,8EAA8E;gBAC9E,MAAMC,gBAAgB,MAAMxK,MAC1B,CAAC,iBAAiB,EAAEsK,UAAU,UAAU,CAAC,EACzCG,IAAI,CAAC,CAAC/M,MAAQA,IAAI2C,IAAI;gBACxB,iFAAiF;gBACjFkK,YAAYC,aAAa,CAAC,EAAE;YAC9B,EAAE,OAAM,CAAC;YACT,IAAID,WAAW;gBACb,IAAI,CAACF,mBAAmB,GAAGE,UAAUF,mBAAmB;YAC1D;QACF;QAEA,MAAM,IAAI,CAAC/C,KAAK,CAAC6C;QACjB,oDAAoD;QACpD,MAAMpO,GAAG2O,KAAK,CAAC,IAAI,CAAC9J,OAAO,EAAE;YAAE+J,WAAW;QAAK;QAE/C,MAAMC,sBAAsB5R,KAAK,IAAI,CAAC4H,OAAO,EAAE;QAC/C,8EAA8E;QAC9E,uDAAuD;QACvD,MAAM7E,GAAG8O,SAAS,CAACD,qBAAqB;QAExC,IAAI,CAACE,oBAAoB,GAAG,MAAM,IAAI,CAACrD,gBAAgB,CAAC0C;QAExD,KAAK,MAAMzJ,UAAU,IAAI,CAACoK,oBAAoB,CAAE;YAC9C,MAAMC,eAAerK,OAAOsK,KAAK;YACjCtK,OAAOsK,KAAK,GAAG,OAAO,GAAGC;oBACJ;gBAAnB,MAAMC,aAAa,EAAA,sBAAA,IAAI,CAACC,aAAa,qBAAlB,oBAAoBD,UAAU,KAAI;gBACrD,MAAME,UAAUtQ,WAAWoQ;gBAC3B,wCAAwC;gBACxC,MAAM/C,cAAc,MAAM4C,gBAAgBE;gBAC1C,MAAMI,sBAAsB3K,OAAOjB,IAAI,KAAKhF,eAAekK,MAAM;gBACjE,MAAM2G,0BAA0B5K,OAAOjB,IAAI,KAAKhF,eAAewO,MAAM;gBACrE,MAAMsC,0BACJ7K,OAAOjB,IAAI,KAAKhF,eAAeyO,UAAU;gBAE3C,MAAMrB,QAAQC,GAAG,CACf0D,OAAOC,IAAI,CAACL,SAASpI,GAAG,CAAC,OAAO0I;oBAC9B,MAAMC,YAAYP,OAAO,CAACM,SAAS;oBACnC,MAAM,EAAEE,UAAU,EAAEC,OAAO,EAAE,GAAGF;oBAEhC,MAAMG,SACJ,sDAAsDtF,IAAI,CACxDkF;oBAEJ,MAAM,GAAGK,IAAI,YAAY,OAAM3I,KAAK,GAAG0I,MAAQ,kCAAkC;;oBAEjF,IAAIC,QAAQtR,eAAekK,MAAM,IAAI,CAAC0G,qBAAqB;oBAC3D,IAAIU,QAAQtR,eAAewO,MAAM,IAAI,CAACqC,yBACpC;oBACF,IAAIS,QAAQtR,eAAeyO,UAAU,IAAI,CAACqC,yBACxC;oBAEF,MAAMS,UAAUL,UAAUM,IAAI,KAAKlR,WAAWmR,KAAK;oBACnD,MAAMC,eAAeR,UAAUM,IAAI,KAAKlR,WAAWqR,WAAW;oBAE9D,0DAA0D;oBAC1D,IAAIJ,SAAS;wBACX,MAAMK,aACJ,CAACR,WAAWhQ,WAAW8P,UAAUW,gBAAgB;wBACnD,IAAI,CAACD,YAAY;4BACf,OAAOjB,OAAO,CAACM,SAAS;4BACxB;wBACF;oBACF;oBAEA,sEAAsE;oBACtE,IAAIS,cAAc;wBAChB,IAAIR,UAAUY,qBAAqB,EAAE;4BACnC,MAAMF,aACJ,CAACR,WAAWhQ,WAAW8P,UAAUY,qBAAqB;4BACxD,IAAI,CAACF,YAAY;gCACf,OAAOjB,OAAO,CAACM,SAAS;gCACxB;4BACF;wBACF;oBACF;oBAEA,8CAA8C;oBAC9C,IAAItI,SAAS,WAAW;wBACtB,IAAI,CAACxB,yBAAyB,GAAG;oBACnC;oBAEA,MAAM4K,YAAY,CAAC,CAAC,IAAI,CAACvL,MAAM;oBAC/B,MAAMwL,YAAYD,aAAaZ,WAAWpN,UAAU,CAAC;oBACrD,MAAMkO,aAAaV,UACf,MAAMrS,8BAA8B;wBAClCgT,gBAAgBF;wBAChB9E,gBAAgB,IAAI,CAACjH,MAAM,CAACiH,cAAc;wBAC1CiF,cAAcjB,UAAUW,gBAAgB;wBACxCrL,QAAQ,IAAI,CAACA,MAAM;wBACnBP,QAAQ,IAAI,CAACA,MAAM;wBACnBqH,OAAO;wBACP3E;oBACF,KACApF;oBAEJ,IAAI0O,CAAAA,8BAAAA,WAAYT,IAAI,MAAK1P,WAAW0L,KAAK,EAAE;4BAEvCyE,2BAAAA,oBACAA,4BAAAA;wBAFF,IACEA,EAAAA,qBAAAA,WAAWhM,MAAM,sBAAjBgM,4BAAAA,mBAAmBhM,MAAM,qBAAzBgM,0BAA2BG,GAAG,MAAK,QACnCH,EAAAA,sBAAAA,WAAWhM,MAAM,sBAAjBgM,6BAAAA,oBAAmBhM,MAAM,qBAAzBgM,2BAA2BG,GAAG,MAAK,UACnC;4BACA,IAAI,CAACnL,iBAAiB,GAAG;wBAC3B;oBACF;oBAEA,MAAMoL,oBACJL,aAAaC,CAAAA,8BAAAA,WAAYK,GAAG,MAAKrS,iBAAiBiK,MAAM;oBAE1D,MAAMqI,WAAuBrB,UAAUC,UAAU,CAACpN,UAAU,CAC1D,YAEEjC,WAAW0L,KAAK,GAChB0D,UAAUC,UAAU,CAACpN,UAAU,CAAC,UAC9BjC,WAAW0Q,GAAG,GACd1Q,WAAW2Q,IAAI;oBAErB,IAAIF,aAAa,SAAS;wBACxB,IAAI,CAACpL,yBAAyB,GAAG;oBACnC;oBACA,IAAIoL,aAAa,OAAO;wBACtB,IAAI,CAACrL,uBAAuB,GAAG;oBACjC;oBAEA,MAAMwL,oBACJ7R,0BAA0B8H,SAAS4J,aAAazQ,WAAW2Q,IAAI;oBAEjE,IAAIE,cAAcV,8BAAAA,WAAYW,OAAO;oBAErC,IACE9R,iBAAiB6H,SACjB,CAAC,IAAI,CAAC1C,MAAM,CAAC4M,YAAY,CAACC,cAAc,IACxCH,gBAAgB,UAChB;wBACAtT,IAAIuM,IAAI,CACN;wBAEF+G,cAAc;oBAChB;oBAEA1T,uBAAuB;wBACrB0J;wBACAgK;wBACAJ;wBACAQ,cAAc;4BACZ,kDAAkD;4BAClD,IAAI,CAACjC,2BAA2B,CAACS,SAAS;4BAC1CZ,OAAO,CAACM,SAAS,CAAC+B,MAAM,GAAG5S;4BAE3B,IAAIsS,mBAAmB;gCACrB,MAAMO,uBAAuB9B,WAAW9F,OAAO,CAAC,QAAQ;gCACxDqC,WAAW,CAACuF,qBAAqB,GAAGpU,mBAAmB;oCACrD0P,cAAcvO,eAAeyO,UAAU;oCACvCzJ,MAAMiO;oCACNC,OAAO/T,wBAAwB;wCAC7B0S,kBAAkBX,UAAUW,gBAAgB;wCAC5CsB,cAAc;wCACd7F,OAAO;oCACT;oCACA+E,mBAAmB;oCACnBN;gCACF;gCACA;4BACF;4BACA,MAAMqB,eAAepB,YACjBhT,YAAY;gCACVgG,MAAMmM;gCACNxI;gCACA0K,UAAUnC,UAAUmC,QAAQ;gCAC5BC,UAAU5U,MAAMH,IAAI,CAClBiB,eACAhB,SACE,IAAI,CAACgI,MAAM,EACX0K,UAAUW,gBAAgB,EAC1BxG,OAAO,CAAC,OAAO;gCAEnB7E,QAAQ,IAAI,CAACA,MAAM;gCACnB0G,gBAAgB,IAAI,CAACjH,MAAM,CAACiH,cAAc;gCAC1CY,SAAS,IAAI,CAAC9H,GAAG;gCACjBsH,OAAO;gCACPiG,cAAc,IAAI,CAACtN,MAAM,CAACuN,UAAU,CAACD,YAAY;gCACjDE,UAAU,IAAI,CAACxN,MAAM,CAACwN,QAAQ;gCAC9BC,aAAa,IAAI,CAACzN,MAAM,CAACyN,WAAW;gCACpCC,kBAAkB,IAAI,CAAC1N,MAAM,CAAC2N,MAAM;gCACpCC,eAAe,EAAE5B,8BAAAA,WAAY4B,eAAe;gCAC5CC,kBAAkBC,OAAOC,IAAI,CAC3BxJ,KAAKyJ,SAAS,CAAChC,CAAAA,8BAAAA,WAAYhJ,UAAU,KAAI,CAAC,IAC1CqB,QAAQ,CAAC;4BACb,GAAG4J,MAAM,GACT3Q;4BAEJmK,WAAW,CAACyD,WAAW,GAAGtS,mBAAmB;gCAC3C0P,cAAcvO,eAAeyO,UAAU;gCACvCzJ,MAAMmM;gCACN+B,OAAOnU,mBAAmB;oCACxB8S,kBAAkBX,UAAUW,gBAAgB;oCAC5C/D,SAAS,IAAI,CAAC9H,GAAG;oCACjBI,SAAS,IAAI,CAACA,OAAO;oCACrB+K;oCACAlL,QAAQ,IAAI,CAACA,MAAM;oCACnBqH,OAAO;oCACP3E;oCACAiF,OAAO,IAAI,CAAC9G,YAAY;oCACxBuL;oCACAe;oCACA7F,WAAWyE,YAAYlQ,WAAW0Q,GAAG,GAAG1Q,WAAW0L,KAAK;oCACxDqG,eAAe,EAAE5B,8BAAAA,WAAY4B,eAAe;gCAC9C;gCACA9B;4BACF;wBACF;wBACAoC,UAAU;4BACR,IAAI,CAACvD,qBAAqB;4BAC1B,IAAIc,cAAc;gCAChBf,OAAO,CAACM,SAAS,CAAC+B,MAAM,GAAG5S;gCAC3BsN,WAAW,CAACyD,WAAW,GAAGtS,mBAAmB;oCAC3CmG,MAAMmM;oCACN5C,cAAcvO,eAAekK,MAAM;oCACnCgJ,OAAOhC,UAAUkD,OAAO;oCACxBrC;gCACF;4BACF,OAAO;gCACLpB,OAAO,CAACM,SAAS,CAAC+B,MAAM,GAAG5S;gCAC3BsN,WAAW,CAACyD,WAAW,GAAGtS,mBAAmB;oCAC3CmG,MAAMmM;oCACN5C,cAAcvO,eAAekK,MAAM;oCACnCgJ,OAAOpU,eAAe;wCACpB+S,kBAAkBX,UAAUW,gBAAgB;wCAC5ClJ;oCACF;oCACAoJ;gCACF;4BACF;wBACF;wBACAsC,UAAU;4BACR,kDAAkD;4BAClD,IAAI,CAACxD,2BAA2B,CAACU,SAAS;4BAC1CZ,OAAO,CAACM,SAAS,CAAC+B,MAAM,GAAG5S;4BAC3B,IAAIkU,kBAAkB9V,SACpByH,OAAOsO,OAAO,EACdrD,UAAUW,gBAAgB;4BAE5B,IACE,CAACpT,WAAW6V,oBACZ,CAACA,gBAAgBvQ,UAAU,CAAC,QAC5B;gCACAuQ,kBAAkB,CAAC,EAAE,EAAEA,iBAAiB;4BAC1C;4BAEA,IAAIpB;4BACJ,IAAIR,mBAAmB;gCACrBQ,QAAQ/T,wBAAwB;oCAC9B0S,kBAAkBX,UAAUW,gBAAgB;oCAC5CsB,cAAc;oCACd7F,OAAO;gCACT;gCACAI,WAAW,CAACyD,WAAW,GAAGtS,mBAAmB;oCAC3C0P,cAAcvO,eAAewO,MAAM;oCACnCxJ,MAAMmM;oCACNkB,mBAAmB;oCACnBa;oCACAnB;gCACF;4BACF,OAAO,IAAIjR,iBAAiB6H,OAAO;gCACjCuK,QAAQnU,mBAAmB;oCACzB8S,kBAAkBX,UAAUW,gBAAgB;oCAC5C/D,SAAS,IAAI,CAAC9H,GAAG;oCACjBI,SAAS,IAAI,CAACA,OAAO;oCACrB+K;oCACAlL,QAAQ,IAAI,CAACA,MAAM;oCACnBqH,OAAO;oCACP3E;oCACAiF,OAAO,IAAI,CAAC9G,YAAY;oCACxBuL;oCACA9E,WAAWzL,WAAW0L,KAAK;oCAC3BqG,eAAe,EAAE5B,8BAAAA,WAAY4B,eAAe;gCAC9C;4BACF,OAAO,IAAI7B,WAAW;gCACpBkB,QAAQlU,YAAY;oCAClBgG,MAAMmM;oCACNxI;oCACA0K,UAAUnC,UAAUmC,QAAQ;oCAC5BC,UAAU5U,MAAMH,IAAI,CAClBiB,eACAhB,SACE,IAAI,CAACgI,MAAM,EACX0K,UAAUW,gBAAgB,EAC1BxG,OAAO,CAAC,OAAO;oCAEnB7E,QAAQ,IAAI,CAACA,MAAM;oCACnB0G,gBAAgB,IAAI,CAACjH,MAAM,CAACiH,cAAc;oCAC1CY,SAAS,IAAI,CAAC9H,GAAG;oCACjBsH,OAAO;oCACPiG,cAAc,IAAI,CAACtN,MAAM,CAACuN,UAAU,CAACD,YAAY;oCACjDE,UAAU,IAAI,CAACxN,MAAM,CAACwN,QAAQ;oCAC9BC,aAAa,IAAI,CAACzN,MAAM,CAACyN,WAAW;oCACpCC,kBAAkB,IAAI,CAAC1N,MAAM,CAAC2N,MAAM;oCACpCC,eAAe,EAAE5B,8BAAAA,WAAY4B,eAAe;oCAC5CC,kBAAkBC,OAAOC,IAAI,CAC3BxJ,KAAKyJ,SAAS,CAAChC,CAAAA,8BAAAA,WAAYhJ,UAAU,KAAI,CAAC,IAC1CqB,QAAQ,CAAC;gCACb;4BACF,OAAO,IAAI9I,WAAWmH,OAAO;gCAC3BuK,QAAQzR,oBAAoB;oCAC1B+S,MAAM5S,UAAU6S,SAAS;oCACzB9L;oCACAkJ,kBAAkByC;oCAClBT,eAAe,EAAE5B,8BAAAA,WAAY4B,eAAe;oCAC5CC,kBAAkB7B,CAAAA,8BAAAA,WAAYhJ,UAAU,KAAI,CAAC;gCAC/C;4BACF,OAAO,IACL,CAACnI,iBAAiB6H,SAClB,CAACjH,oBAAoB4S,oBACrB,CAAC3S,oBAAoBgH,SACrB,CAAC+J,mBACD;gCACAQ,QAAQzR,oBAAoB;oCAC1B+S,MAAM5S,UAAU4L,KAAK;oCACrB7E;oCACAiF,OAAO,IAAI,CAAC9G,YAAY;oCACxB+K,kBAAkByC;oCAClBT,eAAe,EAAE5B,8BAAAA,WAAY4B,eAAe;oCAC5CC,kBAAkB7B,CAAAA,8BAAAA,WAAYhJ,UAAU,KAAI,CAAC;gCAC/C;4BACF,OAAO;gCACLiK,QAAQoB;4BACV;4BAEA5G,WAAW,CAACyD,WAAW,GAAGtS,mBAAmB;gCAC3C0P,cAAcvO,eAAewO,MAAM;gCACnCxJ,MAAMmM;gCACNkB;gCACAa;gCACAnB;4BACF;wBACF;oBACF;gBACF;gBAGF,IAAI,CAAC,IAAI,CAAC9K,iBAAiB,EAAE;oBAC3B,OAAOyG,WAAW,CAAC9N,gCAAgC;gBACrD;gBACA,IAAI,CAAC,IAAI,CAACuH,yBAAyB,EAAE;oBACnC,OAAOuG,WAAW,CAAC7N,iCAAiC;oBACpD,OAAO6N,WAAW,CAAC,aAAa;oBAChC,OAAOA,WAAW,CAAC,eAAe;oBAClC,OAAOA,WAAW,CAAC,UAAU;oBAC7B,OAAOA,WAAW,CAAC,kBAAkB;gBACvC;gBACA,qEAAqE;gBACrE,IAAI,CAAC,IAAI,CAACzG,iBAAiB,IAAI,CAAC,IAAI,CAACE,yBAAyB,EAAE;oBAC9D,OAAOuG,WAAW,CAAC3N,0CAA0C;gBAC/D;gBACA,IAAI,CAAC,IAAI,CAACmH,uBAAuB,EAAE;oBACjC,OAAOwG,WAAW,CAAC5N,qCAAqC;gBAC1D;gBAEA,OAAO4N;YACT;QACF;QAEA,iFAAiF;QACjF,uBAAuB;QACvB,IAAI,CAAC2C,oBAAoB,CAACqE,WAAW,GAAG;QAExC,IAAI,CAAChE,aAAa,GAAGxS,QACnB,IAAI,CAACmS,oBAAoB;QAG3B,uEAAuE;QACvE,MAAMsE,kBAAkB,IAAI,CAACjE,aAAa,CAACkE,SAAS,CAAC,EAAE,CAACD,eAAe;QACvE,KAAK,MAAME,YAAY,IAAI,CAACnE,aAAa,CAACkE,SAAS,CAAE;YACnDC,SAASF,eAAe,GAAGA;YAC3B,qFAAqF;YACrFE,SAASC,WAAW,GAAGC,KAAKC,GAAG;YAC/B,sGAAsG;YACtGH,SAASI,KAAK,CAACC,SAAS,CAACC,SAAS,CAAC;gBACjCC,UAASC,OAAY;oBACnB,IAAIA,QAAQrQ,IAAI,KAAK,yBAAyB;wBAC5C,OAAO;oBACT;oBACA,OAAOqQ;gBACT;YACF;QACF;QAEA,IAAI,CAAC3E,aAAa,CAACuE,KAAK,CAACK,IAAI,CAACC,GAAG,CAAC,qBAAqB;gBACrDZ;YAAAA,oCAAAA,yBAAAA,gBAAiBa,KAAK,qBAAtBb,4BAAAA;QACF;QACAvV,eACE,IAAI,CAACsR,aAAa,CAACkE,SAAS,CAAC,EAAE,EAC/B,IAAI,CAAClE,aAAa,CAACkE,SAAS,CAAC,EAAE,EAC/B,IAAI,CAAClE,aAAa,CAACkE,SAAS,CAAC,EAAE;QAGjC,yEAAyE;QACzE,gEAAgE;QAChE,MAAMa,qBAAqB,IAAIhT;QAC/B,MAAMiT,qBAAqB,IAAIjT;QAC/B,MAAMkT,yBAAyB,IAAIlT;QAEnC,MAAMmT,8BAA8B,IAAInT;QACxC,MAAMoT,wBAAwB,IAAIpT;QAElC,MAAMqT,uBAAuB,IAAIC;QACjC,MAAMC,uBAAuB,IAAID;QACjC,MAAME,2BAA2B,IAAIF;QACrC,MAAMG,4BAA4B,IAAIH;QAEtC,MAAMI,qBAAqB,IAAIC,OAC7B,CAAC,MAAM,EAAE,IAAI,CAACnQ,MAAM,CAACiH,cAAc,CAAC3O,IAAI,CAAC,KAAK,EAAE,CAAC;QAGnD,MAAM8X,mBACJ,CACEC,aACAC,cACAC,8BAEF,CAACC;gBACC,IAAI;oBACFA,MAAM/I,WAAW,CAACgJ,OAAO,CAAC,CAACnG,OAAOe;wBAChC,IACEA,IAAIvN,UAAU,CAAC,aACfuN,IAAIvN,UAAU,CAAC,WACfhD,qBAAqBuQ,MACrB;4BACA,mDAAmD;4BACnDf,MAAMoG,MAAM,CAACD,OAAO,CAAC,CAACE;gCACpB,IAAIA,MAAMhK,EAAE,KAAK0E,KAAK;oCACpB,MAAMuF,eACJJ,MAAMK,UAAU,CAACC,uBAAuB,CAACH;oCAE3C,IAAII,sBAAsB;oCAC1B,IAAIC,aAAa,IAAI9Y;oCACrB,IAAI+Y,wBAAwB,IAAI/Y;oCAEhC0Y,aAAaH,OAAO,CAAC,CAACS;wCACpB,IACEA,IAAIC,QAAQ,IACZD,IAAIC,QAAQ,CAAC/L,OAAO,CAAC,OAAO,KAAKgM,QAAQ,CAAC/F,QAC1C,oCAAoC;wCACpC6E,mBAAmBmB,IAAI,CAACH,IAAIC,QAAQ,GACpC;gDAaED,oBAAAA;4CAZF,uDAAuD;4CACvD,uDAAuD;4CACvD,wDAAwD;4CACxD,sDAAsD;4CACtD,MAAMzN,OAAOrE,QAAQ,UAClBkS,UAAU,CAAC,QACXC,MAAM,CAACL,IAAIM,cAAc,GAAGC,MAAM,IAClCC,MAAM,GACNrN,QAAQ,CAAC;4CAEZ,IACE6M,IAAIS,KAAK,KAAKnY,eAAeoY,qBAAqB,IAClDV,CAAAA,wBAAAA,iBAAAA,IAAKW,SAAS,sBAAdX,qBAAAA,eAAgB7E,GAAG,qBAAnB6E,mBAAqB3F,IAAI,MAAK,UAC9B;gDACA0F,sBAAsBa,GAAG,CAACrO;4CAC5B;4CAEAuN,WAAWc,GAAG,CAACrO;wCACjB,OAAO;gDASHyN,qBAAAA;4CARF,oDAAoD;4CACpD,MAAMzN,OAAO+M,MAAMK,UAAU,CAACkB,aAAa,CACzCb,KACAP,MAAMhE,OAAO;4CAGf,IACEuE,IAAIS,KAAK,KAAKnY,eAAeoY,qBAAqB,IAClDV,CAAAA,wBAAAA,kBAAAA,IAAKW,SAAS,sBAAdX,sBAAAA,gBAAgB7E,GAAG,qBAAnB6E,oBAAqB3F,IAAI,MAAK,UAC9B;gDACA0F,sBAAsBa,GAAG,CAACrO;4CAC5B;4CAEAuN,WAAWc,GAAG,CAACrO;4CAEf,iDAAiD;4CACjD,0BAA0B;4CAC1B,IACE4H,IAAIvN,UAAU,CAAC,WACf,qBAAqBuT,IAAI,CAACH,IAAIC,QAAQ,IAAI,KAC1C;gDACA,MAAMa,cAAcd,IAAIS,KAAK,GAAG,MAAMT,IAAIC,QAAQ;gDAClD,MAAMc,WACJhC,0BAA0BiC,GAAG,CAACF;gDAChC,IAAIC,YAAYA,aAAaxO,MAAM;oDACjCsN,sBAAsB;gDACxB;gDACAd,0BAA0BkC,GAAG,CAACH,aAAavO;4CAC7C;wCACF;oCACF;oCAEA,MAAMwO,WAAW5B,YAAY6B,GAAG,CAAC7G;oCACjC,MAAM+G,UAAUpB,WAAW3M,QAAQ;oCACnC,IAAI4N,YAAYA,aAAaG,SAAS;wCACpC9B,aAAawB,GAAG,CAACzG;oCACnB;oCACAgF,YAAY8B,GAAG,CAAC9G,KAAK+G;oCAErB,IAAI7B,6BAA6B;wCAC/B,MAAM8B,YACJ7Y,eAAeoY,qBAAqB,GAAG,MAAMvG;wCAC/C,MAAMiH,iBAAiBjC,YAAY6B,GAAG,CAACG;wCACvC,MAAME,gBAAgBtB,sBAAsB5M,QAAQ;wCACpD,IAAIiO,kBAAkBA,mBAAmBC,eAAe;4CACtDhC,4BAA4BuB,GAAG,CAACzG;wCAClC;wCACAgF,YAAY8B,GAAG,CAACE,WAAWE;oCAC7B;oCAEA,IAAIxB,qBAAqB;wCACvBnB,sBAAsBkC,GAAG,CAACzG;oCAC5B;gCACF;4BACF;wBACF;oBACF;gBACF,EAAE,OAAOmH,KAAK;oBACZjV,QAAQN,KAAK,CAACuV;gBAChB;YACF;QAEF,IAAI,CAAC/H,aAAa,CAACkE,SAAS,CAAC,EAAE,CAACK,KAAK,CAACyD,IAAI,CAACnD,GAAG,CAC5C,8BACAc,iBAAiBP,sBAAsBL;QAEzC,IAAI,CAAC/E,aAAa,CAACkE,SAAS,CAAC,EAAE,CAACK,KAAK,CAACyD,IAAI,CAACnD,GAAG,CAC5C,8BACAc,iBACEL,sBACAN,oBACAE;QAGJ,IAAI,CAAClF,aAAa,CAACkE,SAAS,CAAC,EAAE,CAACK,KAAK,CAACyD,IAAI,CAACnD,GAAG,CAC5C,8BACAc,iBACEJ,0BACAN,wBACAC;QAIJ,8GAA8G;QAC9G,IAAI,CAAClF,aAAa,CAACkE,SAAS,CAAC,EAAE,CAACK,KAAK,CAAC0D,MAAM,CAACpD,GAAG,CAC9C,8BACA,CAACkD;YACC,IAAI,CAAC7R,WAAW,GAAG6R;YACnB,IAAI,CAACnR,WAAW,GAAG;YACnB,IAAI,CAACsR,gBAAgB,GAAGrV;QAC1B;QAGF,IAAI,CAACmN,aAAa,CAACkE,SAAS,CAAC,EAAE,CAACK,KAAK,CAACK,IAAI,CAACC,GAAG,CAC5C,8BACA,CAACkB;YACC,IAAI,CAAC7P,WAAW,GAAG;YACnB,IAAI,CAACW,eAAe,GAAGkP;QACzB;QAGF,IAAI,CAAC/F,aAAa,CAACkE,SAAS,CAAC,EAAE,CAACK,KAAK,CAACK,IAAI,CAACC,GAAG,CAC5C,8BACA,CAACkB;YACC,IAAI,CAAC7P,WAAW,GAAG;YACnB,IAAI,CAACU,WAAW,GAAGmP;YAEnB,IAAI,CAAC,IAAI,CAACvQ,QAAQ,EAAE;gBAClB;YACF;YAEA,MAAM,EAAE1B,WAAW,EAAE,GAAGiS;YAExB,kEAAkE;YAClE,oEAAoE;YACpE,MAAMoC,gBAAgBrU,YAAYsU,WAAW,CAACX,GAAG,CAAC;YAClD,qDAAqD;YACrD,IAAI,CAACU,eAAe;gBAClB;YACF;YAEA,gBAAgB;YAChB,IAAI,IAAI,CAACrR,sBAAsB,KAAK,MAAM;gBACxC,IAAI,CAACA,sBAAsB,GAAGqR,cAAcnP,IAAI,IAAI;gBACpD;YACF;YAEA,2DAA2D;YAC3D,IAAImP,cAAcnP,IAAI,KAAK,IAAI,CAAClC,sBAAsB,EAAE;gBACtD;YACF;YAEA,6DAA6D;YAC7D,iEAAiE;YACjE,0EAA0E;YAC1E,2EAA2E;YAC3E,IAAI,IAAI,CAAChB,MAAM,EAAE;gBACf,MAAMuS,aAAa,IAAItW,IAAI+B,YAAYsU,WAAW,CAAC9H,IAAI;gBACvD,MAAMgI,iBAAiBpY,WACrB,IAAI,CAACgY,gBAAgB,IAAI,IAAInW,OAC7BsW;gBAGF,IACEC,eAAehQ,MAAM,KAAK,KAC1BgQ,eAAeC,KAAK,CAAC,CAACC,YAAcA,UAAUnV,UAAU,CAAC,UACzD;oBACA;gBACF;gBACA,IAAI,CAAC6U,gBAAgB,GAAGG;YAC1B;YAEA,IAAI,CAACvR,sBAAsB,GAAGqR,cAAcnP,IAAI,IAAI;YAEpD,iFAAiF;YACjF,IAAI,CAACL,IAAI,CAAC;gBACRC,QAAQzH,4BAA4B0H,WAAW;gBAC/CC,MAAM;YACR;QACF;QAGF,IAAI,CAACkH,aAAa,CAACuE,KAAK,CAACK,IAAI,CAACC,GAAG,CAAC,8BAA8B,CAACkB;YAC/D,MAAMzP,0BAA0B,IAAI,CAACA,uBAAuB;YAC5D,IAAI,CAACA,uBAAuB,GAAG;YAE/B,MAAMmS,oBAAoBvY,WACxB8U,oBACAD;YAGF,MAAM2D,wBAAwBxY,WAC5B+U,wBACAF;YAGF,MAAM4D,cAAcF,kBACjBG,MAAM,CAACF,uBACP1W,MAAM,CAAC,CAAC4O,MAAQA,IAAIvN,UAAU,CAAC;YAElC,MAAMwV,oBAAoB;mBACrBC,MAAMxF,IAAI,CAAC2B;mBACX6D,MAAMxF,IAAI,CAAC0B;aACf,CAAChT,MAAM,CAAC,CAACsC,OAASjE,qBAAqBiE;YAExC,IAAIuU,kBAAkBvQ,MAAM,GAAG,GAAG;gBAChC,IAAI,CAACK,IAAI,CAAC;oBACRsB,OAAO9I,4BAA4B4X,kBAAkB;gBACvD;YACF;YAEA,IAAIJ,YAAYrQ,MAAM,GAAG,GAAG;gBAC1B,IAAI,CAACK,IAAI,CAAC;oBACRsB,OAAO9I,4BAA4B6X,mBAAmB;oBACtD9L,OAAOuL,kBAAkB5Q,GAAG,CAAC,CAACoR,KAC5BlZ,oBAAoBkZ,GAAGpN,KAAK,CAAC,QAAQvD,MAAM;gBAE/C;YACF;YAEA,IACE4M,4BAA4BgE,IAAI,IAChC/D,sBAAsB+D,IAAI,IAC1B5S,yBACA;gBACA,IAAI,CAACN,UAAU;gBACf,IAAI,CAAC+C,uBAAuB,CAACgN,MAAM/M,IAAI;YACzC;YAEA+L,mBAAmBoE,KAAK;YACxBnE,mBAAmBmE,KAAK;YACxBlE,uBAAuBkE,KAAK;YAC5BjE,4BAA4BiE,KAAK;YACjChE,sBAAsBgE,KAAK;QAC7B;QAEA,IAAI,CAACnJ,aAAa,CAACkE,SAAS,CAAC,EAAE,CAACK,KAAK,CAAC0D,MAAM,CAACpD,GAAG,CAC9C,8BACA,CAACkD;YACC,IAAI,CAAC9R,WAAW,GAAG8R;YACnB,IAAI,CAACpR,WAAW,GAAG;QACrB;QAEF,IAAI,CAACqJ,aAAa,CAACkE,SAAS,CAAC,EAAE,CAACK,KAAK,CAACK,IAAI,CAACC,GAAG,CAC5C,8BACA,CAACkB;YACC,IAAI,CAAC9P,WAAW,GAAG;YACnB,IAAI,CAACU,WAAW,GAAGoP;YAEnB,MAAM,EAAEjS,WAAW,EAAE,GAAGiS;YACxB,MAAMsC,aAAa,IAAItW,IACrB;mBAAI+B,YAAYsU,WAAW,CAAC9H,IAAI;aAAG,CAACtO,MAAM,CACxC,CAACsC,OAAS,CAAC,CAACrE,uBAAuBqE;YAIvC,IAAI,IAAI,CAAC8U,cAAc,EAAE;gBACvB,8DAA8D;gBAC9D,0CAA0C;gBAC1C,MAAMC,aAAazX,KAAKyW,YAAY,IAAI,CAACe,cAAc;gBACvD,MAAME,eAAe1X,KAAK,IAAI,CAACwX,cAAc,EAAGf;gBAEhD,IAAIgB,WAAWH,IAAI,GAAG,GAAG;oBACvB,KAAK,MAAMK,aAAaF,WAAY;wBAClC,MAAMpR,OAAOhI,uBAAuBsZ;wBACpC,IAAI,CAAC5Q,IAAI,CAAC;4BACRC,QAAQzH,4BAA4BqY,UAAU;4BAC9C1Q,MAAM;gCAACb;6BAAK;wBACd;oBACF;gBACF;gBAEA,IAAIqR,aAAaJ,IAAI,GAAG,GAAG;oBACzB,KAAK,MAAMO,eAAeH,aAAc;wBACtC,MAAMrR,OAAOhI,uBAAuBwZ;wBACpC,IAAI,CAAC9Q,IAAI,CAAC;4BACRC,QAAQzH,4BAA4BuY,YAAY;4BAChD5Q,MAAM;gCAACb;6BAAK;wBACd;oBACF;gBACF;YACF;YAEA,IAAI,CAACmR,cAAc,GAAGf;QACxB;QAGF,IAAI,CAAC5O,oBAAoB,GAAG,IAAI7L,qBAC9B,IAAI,CAACoS,aAAa,CAACkE,SAAS,EAC5B,IAAI,CAAC7N,WAAW,EAChB,IAAI,CAAC6I,mBAAmB;QAG1B,IAAIyK,SAAS;QAEb,IAAI,CAACC,OAAO,GAAG,MAAM,IAAIlN,QAAQ,CAAC8B;gBAChB;YAAhB,MAAMoL,WAAU,sBAAA,IAAI,CAAC5J,aAAa,qBAAlB,oBAAoBtB,KAAK,CACvC,kFAAkF;YAClF,IAAI,CAACiB,oBAAoB,CAAC9H,GAAG,CAAC,CAACtC,SAAWA,OAAOoJ,YAAY,GAC7D,gCAAgC;YAChC,CAACC;gBACC,IAAI,CAAC+K,QAAQ;oBACXA,SAAS;oBACTnL,QAAQoL;gBACV;YACF;QAEJ;QAEA,IAAI,CAAClQ,eAAe,GAAG5J,qBAAqB;YAC1C+Z,aAAa,IAAI;YACjB7J,eAAe,IAAI,CAACA,aAAa;YACjCxK,UAAU,IAAI,CAACA,QAAQ;YACvBM,QAAQ,IAAI,CAACA,MAAM;YACnBsH,SAAS,IAAI,CAAC9H,GAAG;YACjBwU,YAAY,IAAI,CAACvU,MAAM;YACvB,GAAI,IAAI,CAACA,MAAM,CAACmE,eAAe;QAIjC;QAEA,IAAI,CAAChD,WAAW,GAAG;YACjBhJ,qBAAqB;gBACnBqc,eAAe,IAAI,CAACzU,GAAG;gBACvBqB,aAAa,IAAM,IAAI,CAACA,WAAW;gBACnCC,aAAa,IAAM,IAAI,CAACA,WAAW;gBACnCC,iBAAiB,IAAM,IAAI,CAACA,eAAe;YAC7C;YACAlJ,uBAAuB;gBACrBgJ,aAAa,IAAM,IAAI,CAACA,WAAW;gBACnCC,aAAa,IAAM,IAAI,CAACA,WAAW;gBACnCC,iBAAiB,IAAM,IAAI,CAACA,eAAe;YAC7C;YACAtF,+BAA+B,IAAI,CAACwE,SAAS;YAC7CvE;YACAC;SACD;IACH;IAEOuY,WACL,EAAE1T,uBAAuB,EAAwC,GAAG;QAClEA,yBAAyB;IAC3B,CAAC,EACD;YAGmB;QAFnB,mGAAmG;QACnG,IAAI,CAACA,uBAAuB,GAAGA;QAC/B,MAAMyJ,cAAa,sBAAA,IAAI,CAACC,aAAa,qBAAlB,oBAAoBD,UAAU;QACjD,IAAIA,YAAY;gBACdlQ;aAAAA,kBAAAA,eAAekQ,gCAAflQ,gBAA4Bma,UAAU;QACxC;IACF;IAEA,MAAa3R,qBAAqBJ,IAAY,EAAE;YAcnC,mBAEA,mBAEA;QAjBX,MAAMgS,YAAY,CAAC,EAAEnW,WAAW,EAAiB;gBAIxCK;YAHP,MAAMA,cAAcD,aAAaJ;YACjC,MAAMoW,iBAAiBla,iBAAiBiI;YACxC,+FAA+F;YAC/F,OAAO9D,EAAAA,8BAAAA,WAAW,CAAC+V,eAAe,qBAA3B/V,4BAA6BmE,MAAM,IAAG,IACzCnE,WAAW,CAAC+V,eAAe,GAC3BpW,YAAYM,MAAM;QACxB;QAEA,IAAI,IAAI,CAAC6B,WAAW,EAAE;YACpB,OAAO;gBAAC,IAAI,CAACA,WAAW;aAAC;QAC3B,OAAO,IAAI,IAAI,CAACC,WAAW,EAAE;YAC3B,OAAO;gBAAC,IAAI,CAACA,WAAW;aAAC;QAC3B,OAAO,KAAI,oBAAA,IAAI,CAACS,WAAW,qBAAhB,kBAAkBwT,SAAS,IAAI;YACxC,OAAOF,UAAU,IAAI,CAACtT,WAAW;QACnC,OAAO,KAAI,oBAAA,IAAI,CAACC,WAAW,qBAAhB,kBAAkBuT,SAAS,IAAI;YACxC,OAAOF,UAAU,IAAI,CAACrT,WAAW;QACnC,OAAO,KAAI,wBAAA,IAAI,CAACC,eAAe,qBAApB,sBAAsBsT,SAAS,IAAI;YAC5C,OAAOF,UAAU,IAAI,CAACpT,eAAe;QACvC,OAAO;YACL,OAAO,EAAE;QACX;IACF;IAEO8B,KAAKC,MAAwB,EAAQ;QAC1C,IAAI,CAACa,oBAAoB,CAAE2Q,OAAO,CAACxR;IACrC;IAEA,MAAaT,WAAW,EACtBF,IAAI,EACJG,UAAU,EACVuK,QAAQ,EACR0H,UAAU,EACVC,KAAK,EACLlX,GAAG,EAQJ,EAAiB;QAChB,OAAO,IAAI,CAAC2D,eAAe,CACxBiD,UAAU,CAAC,eAAe;YACzBuQ,WAAWtS;QACb,GACCoE,YAAY,CAAC;gBAYL;YAXP,wDAAwD;YACxD,IAAIpE,SAAS,aAAahJ,cAAciJ,OAAO,CAACD,UAAU,CAAC,GAAG;gBAC5D;YACF;YACA,MAAMzF,QAAQ4F,aACV,IAAI,CAACnC,WAAW,GAChB,IAAI,CAACC,WAAW,IAAI,IAAI,CAACD,WAAW;YACxC,IAAIzD,OAAO;gBACT,MAAMA;YACR;YAEA,QAAO,wBAAA,IAAI,CAACkH,eAAe,qBAApB,sBAAsBvB,UAAU,CAAC;gBACtCF;gBACA0K;gBACA0H;gBACAC;gBACAlX;YACF;QACF;IACJ;IAEOoX,QAAQ;YACb;SAAA,6BAAA,IAAI,CAAC/Q,oBAAoB,qBAAzB,2BAA2B+Q,KAAK;IAClC;AACF"}