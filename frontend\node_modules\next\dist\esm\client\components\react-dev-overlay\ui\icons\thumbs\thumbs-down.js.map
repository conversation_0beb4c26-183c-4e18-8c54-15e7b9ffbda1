{"version": 3, "sources": ["../../../../../../../src/client/components/react-dev-overlay/ui/icons/thumbs/thumbs-down.tsx"], "sourcesContent": ["import type { ComponentProps } from 'react'\n\nexport function ThumbsDown(props: ComponentProps<'svg'>) {\n  return (\n    <svg\n      width=\"16\"\n      height=\"16\"\n      viewBox=\"0 0 16 16\"\n      fill=\"none\"\n      xmlns=\"http://www.w3.org/2000/svg\"\n      className=\"thumbs-down-icon\"\n      {...props}\n    >\n      <path\n        fillRule=\"evenodd\"\n        clipRule=\"evenodd\"\n        d=\"M5.89531 12.7603C5.72984 12.8785 5.5 12.7602 5.5 12.5569V9.75C5.5 8.7835 4.7165 8 3.75 8H1.5V1.5H11.1884C11.762 1.5 12.262 1.89037 12.4011 2.44683L13.4011 6.44683C13.5984 7.23576 13.0017 8 12.1884 8H8.25H7.5V8.75V11.4854C7.5 11.5662 7.46101 11.6419 7.39531 11.6889L5.89531 12.7603ZM4 12.5569C4 13.9803 5.6089 14.8082 6.76717 13.9809L8.26717 12.9095C8.72706 12.581 9 12.0506 9 11.4854V9.5H12.1884C13.9775 9.5 15.2903 7.81868 14.8563 6.08303L13.8563 2.08303C13.5503 0.858816 12.4503 0 11.1884 0H0.75H0V0.75V8.75V9.5H0.75H3.75C3.88807 9.5 4 9.61193 4 9.75V12.5569Z\"\n        fill=\"currentColor\"\n      />\n    </svg>\n  )\n}\n"], "names": ["ThumbsDown", "props", "svg", "width", "height", "viewBox", "fill", "xmlns", "className", "path", "fillRule", "clipRule", "d"], "mappings": ";AAEA,OAAO,SAASA,WAAWC,KAA4B;IACrD,qBACE,KAACC;QACCC,OAAM;QACNC,QAAO;QACPC,SAAQ;QACRC,MAAK;QACLC,OAAM;QACNC,WAAU;QACT,GAAGP,KAAK;kBAET,cAAA,KAACQ;YACCC,UAAS;YACTC,UAAS;YACTC,GAAE;YACFN,MAAK;;;AAIb"}