{"version": 3, "sources": ["../../../../../../../../src/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/use-minimum-loading-time-multiple.tsx"], "sourcesContent": ["import { useEffect, useRef, useState } from 'react'\n\n/**\n * A React hook that ensures a loading state persists\n * at least up to the next multiple of a given interval (default: 750ms).\n *\n * For example, if you're done loading at 1200ms, it forces you to wait\n * until 1500ms. If it’s 1800ms, it waits until 2250ms, etc.\n *\n * @param isLoadingTrigger - <PERSON><PERSON>an that triggers the loading state\n * @param interval - The time interval multiple in ms (default: 750ms)\n * @returns Current loading state that respects multiples of the interval\n */\nexport function useMinimumLoadingTimeMultiple(\n  isLoadingTrigger: boolean,\n  interval = 750\n) {\n  const [isLoading, setIsLoading] = useState(false)\n  const loadStartTimeRef = useRef<number | null>(null)\n  const timeoutIdRef = useRef<NodeJS.Timeout | null>(null)\n\n  useEffect(() => {\n    // Clear any pending timeout to avoid overlap\n    if (timeoutIdRef.current) {\n      clearTimeout(timeoutIdRef.current)\n      timeoutIdRef.current = null\n    }\n\n    if (isLoadingTrigger) {\n      // If we enter \"loading\" state, record start time if not already\n      if (loadStartTimeRef.current === null) {\n        loadStartTimeRef.current = Date.now()\n      }\n      setIsLoading(true)\n    } else {\n      // If we're exiting the \"loading\" state:\n      if (loadStartTimeRef.current === null) {\n        // No start time was recorded, so just stop loading immediately\n        setIsLoading(false)\n      } else {\n        // How long we've been \"loading\"\n        const timeDiff = Date.now() - loadStartTimeRef.current\n\n        // Next multiple of `interval` after `timeDiff`\n        const nextMultiple = interval * Math.ceil(timeDiff / interval)\n\n        // Remaining time needed to reach that multiple\n        const remainingTime = nextMultiple - timeDiff\n\n        if (remainingTime > 0) {\n          // If not yet at that multiple, schedule the final step\n          timeoutIdRef.current = setTimeout(() => {\n            setIsLoading(false)\n            loadStartTimeRef.current = null\n          }, remainingTime)\n        } else {\n          // We're already past the multiple boundary\n          setIsLoading(false)\n          loadStartTimeRef.current = null\n        }\n      }\n    }\n\n    // Cleanup when effect is about to re-run or component unmounts\n    return () => {\n      if (timeoutIdRef.current) {\n        clearTimeout(timeoutIdRef.current)\n      }\n    }\n  }, [isLoadingTrigger, interval])\n\n  return isLoading\n}\n"], "names": ["useEffect", "useRef", "useState", "useMinimumLoadingTimeMultiple", "isLoadingTrigger", "interval", "isLoading", "setIsLoading", "loadStartTimeRef", "timeoutIdRef", "current", "clearTimeout", "Date", "now", "timeDiff", "nextMultiple", "Math", "ceil", "remainingTime", "setTimeout"], "mappings": "AAAA,SAASA,SAAS,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,QAAO;AAEnD;;;;;;;;;;CAUC,GACD,OAAO,SAASC,8BACdC,gBAAyB,EACzBC,QAAc;IAAdA,IAAAA,qBAAAA,WAAW;IAEX,MAAM,CAACC,WAAWC,aAAa,GAAGL,SAAS;IAC3C,MAAMM,mBAAmBP,OAAsB;IAC/C,MAAMQ,eAAeR,OAA8B;IAEnDD,UAAU;QACR,6CAA6C;QAC7C,IAAIS,aAAaC,OAAO,EAAE;YACxBC,aAAaF,aAAaC,OAAO;YACjCD,aAAaC,OAAO,GAAG;QACzB;QAEA,IAAIN,kBAAkB;YACpB,gEAAgE;YAChE,IAAII,iBAAiBE,OAAO,KAAK,MAAM;gBACrCF,iBAAiBE,OAAO,GAAGE,KAAKC,GAAG;YACrC;YACAN,aAAa;QACf,OAAO;YACL,wCAAwC;YACxC,IAAIC,iBAAiBE,OAAO,KAAK,MAAM;gBACrC,+DAA+D;gBAC/DH,aAAa;YACf,OAAO;gBACL,gCAAgC;gBAChC,MAAMO,WAAWF,KAAKC,GAAG,KAAKL,iBAAiBE,OAAO;gBAEtD,+CAA+C;gBAC/C,MAAMK,eAAeV,WAAWW,KAAKC,IAAI,CAACH,WAAWT;gBAErD,+CAA+C;gBAC/C,MAAMa,gBAAgBH,eAAeD;gBAErC,IAAII,gBAAgB,GAAG;oBACrB,uDAAuD;oBACvDT,aAAaC,OAAO,GAAGS,WAAW;wBAChCZ,aAAa;wBACbC,iBAAiBE,OAAO,GAAG;oBAC7B,GAAGQ;gBACL,OAAO;oBACL,2CAA2C;oBAC3CX,aAAa;oBACbC,iBAAiBE,OAAO,GAAG;gBAC7B;YACF;QACF;QAEA,+DAA+D;QAC/D,OAAO;YACL,IAAID,aAAaC,OAAO,EAAE;gBACxBC,aAAaF,aAAaC,OAAO;YACnC;QACF;IACF,GAAG;QAACN;QAAkBC;KAAS;IAE/B,OAAOC;AACT"}