{"version": 3, "sources": ["../../../../../../src/client/components/react-dev-overlay/ui/hooks/use-on-click-outside.ts"], "sourcesContent": ["import * as React from 'react'\n\nexport function useOnClickOutside(\n  el: Node | null,\n  cssSelectorsToExclude: string[],\n  handler: ((e: MouseEvent | TouchEvent) => void) | undefined\n) {\n  React.useEffect(() => {\n    if (el == null || handler == null) {\n      return\n    }\n\n    const listener = (e: MouseEvent | TouchEvent) => {\n      // Do nothing if clicking ref's element or descendent elements\n      if (!el || el.contains(e.target as Element)) {\n        return\n      }\n\n      if (\n        // Do nothing if clicking on an element that is excluded by the CSS selector(s)\n        cssSelectorsToExclude.some((cssSelector) =>\n          (e.target as Element).closest(cssSelector)\n        )\n      ) {\n        return\n      }\n\n      handler(e)\n    }\n\n    const root = el.getRootNode()\n    root.addEventListener('mouseup', listener as EventListener)\n    root.addEventListener('touchend', listener as EventListener, {\n      passive: false,\n    })\n    return function () {\n      root.removeEventListener('mouseup', listener as EventListener)\n      root.removeEventListener('touchend', listener as EventListener)\n    }\n  }, [handler, el, cssSelectorsToExclude])\n}\n"], "names": ["React", "useOnClickOutside", "el", "cssSelectorsToExclude", "handler", "useEffect", "listener", "e", "contains", "target", "some", "cssSelector", "closest", "root", "getRootNode", "addEventListener", "passive", "removeEventListener"], "mappings": "AAAA,YAAYA,WAAW,QAAO;AAE9B,OAAO,SAASC,kBACdC,EAAe,EACfC,qBAA+B,EAC/BC,OAA2D;IAE3DJ,MAAMK,SAAS,CAAC;QACd,IAAIH,MAAM,QAAQE,WAAW,MAAM;YACjC;QACF;QAEA,MAAME,WAAW,CAACC;YAChB,8DAA8D;YAC9D,IAAI,CAACL,MAAMA,GAAGM,QAAQ,CAACD,EAAEE,MAAM,GAAc;gBAC3C;YACF;YAEA,IACE,+EAA+E;YAC/EN,sBAAsBO,IAAI,CAAC,CAACC,cAC1B,AAACJ,EAAEE,MAAM,CAAaG,OAAO,CAACD,eAEhC;gBACA;YACF;YAEAP,QAAQG;QACV;QAEA,MAAMM,OAAOX,GAAGY,WAAW;QAC3BD,KAAKE,gBAAgB,CAAC,WAAWT;QACjCO,KAAKE,gBAAgB,CAAC,YAAYT,UAA2B;YAC3DU,SAAS;QACX;QACA,OAAO;YACLH,KAAKI,mBAAmB,CAAC,WAAWX;YACpCO,KAAKI,mBAAmB,CAAC,YAAYX;QACvC;IACF,GAAG;QAACF;QAASF;QAAIC;KAAsB;AACzC"}