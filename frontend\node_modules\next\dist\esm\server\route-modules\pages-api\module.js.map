{"version": 3, "sources": ["../../../../src/server/route-modules/pages-api/module.ts"], "sourcesContent": ["import type { IncomingMessage, ServerResponse } from 'http'\nimport type { PagesAPIRouteDefinition } from '../../route-definitions/pages-api-route-definition'\nimport type { PageConfig } from '../../../types'\nimport type { ParsedUrlQuery } from 'querystring'\nimport { wrapApiHandler, type __ApiPreviewProps } from '../../api-utils'\nimport type { RouteModuleOptions } from '../route-module'\n\nimport { RouteModule, type RouteModuleHandleContext } from '../route-module'\nimport { apiResolver } from '../../api-utils/node/api-resolver'\n\ntype PagesAPIHandleFn = (\n  req: IncomingMessage,\n  res: ServerResponse\n) => Promise<void>\n\n/**\n * The PagesAPIModule is the type of the module exported by the bundled Pages\n * API module.\n */\nexport type PagesAPIModule = typeof import('../../../build/templates/pages-api')\n\ntype PagesAPIUserlandModule = {\n  /**\n   * The exported handler method.\n   */\n  readonly default: PagesAPIHandleFn\n\n  /**\n   * The exported page config.\n   */\n  readonly config?: PageConfig\n}\n\ntype PagesAPIRouteHandlerContext = RouteModuleHandleContext & {\n  /**\n   * The incoming server request in non-edge runtime.\n   */\n  req?: IncomingMessage\n\n  /**\n   * The outgoing server response in non-edge runtime.\n   */\n  res?: ServerResponse\n\n  /**\n   * The revalidate method used by the `revalidate` API.\n   *\n   * @param config the configuration for the revalidation\n   */\n  revalidate: (config: {\n    urlPath: string\n    revalidateHeaders: { [key: string]: string | string[] }\n    opts: { unstable_onlyGenerated?: boolean }\n  }) => Promise<void>\n\n  /**\n   * The hostname for the request.\n   */\n  hostname?: string\n\n  /**\n   * Keys allowed in the revalidate call.\n   */\n  allowedRevalidateHeaderKeys?: string[]\n\n  /**\n   * Whether to trust the host header.\n   */\n  trustHostHeader?: boolean\n\n  /**\n   * The query for the request.\n   */\n  query: ParsedUrlQuery\n\n  /**\n   * The preview props used by the `preview` API.\n   */\n  previewProps: __ApiPreviewProps\n\n  /**\n   * True if the server is in development mode.\n   */\n  dev: boolean\n\n  /**\n   * True if the server is in minimal mode.\n   */\n  minimalMode: boolean\n\n  /**\n   * The page that's being rendered.\n   */\n  page: string\n\n  /**\n   * The error handler for the request.\n   */\n  onError?: Parameters<typeof apiResolver>[8]\n\n  /**\n   * whether multi-zone flag is enabled for draft mode\n   */\n  multiZoneDraftMode?: boolean\n}\n\nexport type PagesAPIRouteModuleOptions = RouteModuleOptions<\n  PagesAPIRouteDefinition,\n  PagesAPIUserlandModule\n>\n\nexport class PagesAPIRouteModule extends RouteModule<\n  PagesAPIRouteDefinition,\n  PagesAPIUserlandModule\n> {\n  private apiResolverWrapped: typeof apiResolver\n\n  constructor(options: PagesAPIRouteModuleOptions) {\n    super(options)\n\n    if (typeof options.userland.default !== 'function') {\n      throw new Error(\n        `Page ${options.definition.page} does not export a default function.`\n      )\n    }\n\n    this.apiResolverWrapped = wrapApiHandler(\n      options.definition.page,\n      apiResolver\n    )\n  }\n\n  /**\n   *\n   * @param req the incoming server request\n   * @param res the outgoing server response\n   * @param context the context for the render\n   */\n  public async render(\n    req: IncomingMessage,\n    res: ServerResponse,\n    context: PagesAPIRouteHandlerContext\n  ): Promise<void> {\n    const { apiResolverWrapped } = this\n    await apiResolverWrapped(\n      req,\n      res,\n      context.query,\n      this.userland,\n      {\n        ...context.previewProps,\n        revalidate: context.revalidate,\n        trustHostHeader: context.trustHostHeader,\n        allowedRevalidateHeaderKeys: context.allowedRevalidateHeaderKeys,\n        hostname: context.hostname,\n        multiZoneDraftMode: context.multiZoneDraftMode,\n        dev: context.dev,\n      },\n      context.minimalMode,\n      context.dev,\n      context.page,\n      context.onError\n    )\n  }\n}\n\nexport default PagesAPIRouteModule\n"], "names": ["wrapApiHandler", "RouteModule", "apiResolver", "PagesAPIRouteModule", "constructor", "options", "userland", "default", "Error", "definition", "page", "apiResolverWrapped", "render", "req", "res", "context", "query", "previewProps", "revalidate", "trustHostHeader", "allowedRevalidateHeaderKeys", "hostname", "multiZoneDraftMode", "dev", "minimalMode", "onError"], "mappings": "AAIA,SAASA,cAAc,QAAgC,kBAAiB;AAGxE,SAASC,WAAW,QAAuC,kBAAiB;AAC5E,SAASC,WAAW,QAAQ,oCAAmC;AAuG/D,OAAO,MAAMC,4BAA4BF;IAMvCG,YAAYC,OAAmC,CAAE;QAC/C,KAAK,CAACA;QAEN,IAAI,OAAOA,QAAQC,QAAQ,CAACC,OAAO,KAAK,YAAY;YAClD,MAAM,qBAEL,CAFK,IAAIC,MACR,CAAC,KAAK,EAAEH,QAAQI,UAAU,CAACC,IAAI,CAAC,oCAAoC,CAAC,GADjE,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,IAAI,CAACC,kBAAkB,GAAGX,eACxBK,QAAQI,UAAU,CAACC,IAAI,EACvBR;IAEJ;IAEA;;;;;GAKC,GACD,MAAaU,OACXC,GAAoB,EACpBC,GAAmB,EACnBC,OAAoC,EACrB;QACf,MAAM,EAAEJ,kBAAkB,EAAE,GAAG,IAAI;QACnC,MAAMA,mBACJE,KACAC,KACAC,QAAQC,KAAK,EACb,IAAI,CAACV,QAAQ,EACb;YACE,GAAGS,QAAQE,YAAY;YACvBC,YAAYH,QAAQG,UAAU;YAC9BC,iBAAiBJ,QAAQI,eAAe;YACxCC,6BAA6BL,QAAQK,2BAA2B;YAChEC,UAAUN,QAAQM,QAAQ;YAC1BC,oBAAoBP,QAAQO,kBAAkB;YAC9CC,KAAKR,QAAQQ,GAAG;QAClB,GACAR,QAAQS,WAAW,EACnBT,QAAQQ,GAAG,EACXR,QAAQL,IAAI,EACZK,QAAQU,OAAO;IAEnB;AACF;AAEA,eAAetB,oBAAmB"}