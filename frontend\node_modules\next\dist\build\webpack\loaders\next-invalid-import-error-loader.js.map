{"version": 3, "sources": ["../../../../src/build/webpack/loaders/next-invalid-import-error-loader.ts"], "sourcesContent": ["export default function nextInvalidImportErrorLoader(this: any) {\n  const { message } = this.getOptions()\n  throw new Error(message)\n}\n"], "names": ["nextInvalidImportErrorLoader", "message", "getOptions", "Error"], "mappings": ";;;;+BAAA;;;eAAwBA;;;AAAT,SAASA;IACtB,MAAM,EAAEC,OAAO,EAAE,GAAG,IAAI,CAACC,UAAU;IACnC,MAAM,qBAAkB,CAAlB,IAAIC,MAAMF,UAAV,qBAAA;eAAA;oBAAA;sBAAA;IAAiB;AACzB"}