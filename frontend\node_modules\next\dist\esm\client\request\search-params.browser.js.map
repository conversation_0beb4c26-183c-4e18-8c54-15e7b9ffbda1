{"version": 3, "sources": ["../../../src/client/request/search-params.browser.ts"], "sourcesContent": ["export const createRenderSearchParamsFromClient =\n  process.env.NODE_ENV === 'development'\n    ? (\n        require('./search-params.browser.dev') as typeof import('./search-params.browser.dev')\n      ).makeUntrackedExoticSearchParamsWithDevWarnings\n    : (\n        require('./search-params.browser.prod') as typeof import('./search-params.browser.prod')\n      ).makeUntrackedExoticSearchParams\n"], "names": ["createRenderSearchParamsFromClient", "process", "env", "NODE_ENV", "require", "makeUntrackedExoticSearchParamsWithDevWarnings", "makeUntrackedExoticSearchParams"], "mappings": "AAAA,OAAO,MAAMA,qCACXC,QAAQC,GAAG,CAACC,QAAQ,KAAK,gBACrB,AACEC,QAAQ,+BACRC,8CAA8C,GAChD,AACED,QAAQ,gCACRE,+BAA+B,CAAA"}