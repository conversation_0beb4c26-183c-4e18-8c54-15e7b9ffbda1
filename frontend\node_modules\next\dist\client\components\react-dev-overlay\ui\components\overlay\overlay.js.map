{"version": 3, "sources": ["../../../../../../../src/client/components/react-dev-overlay/ui/components/overlay/overlay.tsx"], "sourcesContent": ["import * as React from 'react'\nimport { lock, unlock } from './body-locker'\n\nexport type OverlayProps = {\n  children?: React.ReactNode\n  className?: string\n  fixed?: boolean\n}\n\nconst Overlay: React.FC<OverlayProps> = function Overlay({\n  className,\n  children,\n  fixed,\n  ...props\n}) {\n  React.useEffect(() => {\n    lock()\n    return () => {\n      unlock()\n    }\n  }, [])\n\n  return (\n    <div data-nextjs-dialog-overlay className={className} {...props}>\n      <div\n        data-nextjs-dialog-backdrop\n        data-nextjs-dialog-backdrop-fixed={fixed ? true : undefined}\n      />\n      {children}\n    </div>\n  )\n}\n\nexport { Overlay }\n"], "names": ["Overlay", "className", "children", "fixed", "props", "React", "useEffect", "lock", "unlock", "div", "data-nextjs-dialog-overlay", "data-nextjs-dialog-backdrop", "data-nextjs-dialog-backdrop-fixed", "undefined"], "mappings": ";;;;+BAiCSA;;;eAAAA;;;;;iEAjCc;4BACM;AAQ7B,MAAMA,UAAkC,SAASA,QAAQ,KAKxD;IALwD,IAAA,EACvDC,SAAS,EACTC,QAAQ,EACRC,KAAK,EACL,GAAGC,OACJ,GALwD;IAMvDC,OAAMC,SAAS,CAAC;QACdC,IAAAA,gBAAI;QACJ,OAAO;YACLC,IAAAA,kBAAM;QACR;IACF,GAAG,EAAE;IAEL,qBACE,sBAACC;QAAIC,4BAA0B;QAACT,WAAWA;QAAY,GAAGG,KAAK;;0BAC7D,qBAACK;gBACCE,6BAA2B;gBAC3BC,qCAAmCT,QAAQ,OAAOU;;YAEnDX;;;AAGP"}