{"version": 3, "sources": ["../../../../src/shared/lib/segment-cache/segment-value-encoding.ts"], "sourcesContent": ["import { PAGE_SEGMENT_KEY } from '../segment'\nimport type { Segment as FlightRouterStateSegment } from '../../../server/app-render/types'\n\n// TypeScript trick to simulate opaque types, like in Flow.\ntype Opaque<K, T> = T & { __brand: K }\n\nexport type EncodedSegment = Opaque<'EncodedSegment', string>\n\nexport function encodeSegment(\n  segment: FlightRouterStateSegment\n): EncodedSegment {\n  if (typeof segment === 'string') {\n    if (segment.startsWith(PAGE_SEGMENT_KEY)) {\n      // The Flight Router State type sometimes includes the search params in\n      // the page segment. However, the Segment Cache tracks this as a separate\n      // key. So, we strip the search params here, and then add them back when\n      // the cache entry is turned back into a FlightRouterState. This is an\n      // unfortunate consequence of the FlightRouteState being used both as a\n      // transport type and as a cache key; we'll address this once more of the\n      // Segment Cache implementation has settled.\n      // TODO: We should hoist the search params out of the FlightRouterState\n      // type entirely, This is our plan for dynamic route params, too.\n      return PAGE_SEGMENT_KEY as EncodedSegment\n    }\n    const safeName =\n      // TODO: FlightRouterState encodes Not Found routes as \"/_not-found\".\n      // But params typically don't include the leading slash. We should use\n      // a different encoding to avoid this special case.\n      segment === '/_not-found'\n        ? '_not-found'\n        : encodeToFilesystemAndURLSafeString(segment)\n    // Since this is not a dynamic segment, it's fully encoded. It does not\n    // need to be \"hydrated\" with a param value.\n    return safeName as EncodedSegment\n  }\n  const name = segment[0]\n  const paramValue = segment[1]\n  const paramType = segment[2]\n  const safeName = encodeToFilesystemAndURLSafeString(name)\n  const safeValue = encodeToFilesystemAndURLSafeString(paramValue)\n\n  const encodedName = '$' + paramType + '$' + safeName + '$' + safeValue\n  return encodedName as EncodedSegment\n}\n\nexport const ROOT_SEGMENT_KEY = ''\n\nexport function encodeChildSegmentKey(\n  // TODO: Make segment keys an opaque type, too?\n  parentSegmentKey: string,\n  parallelRouteKey: string,\n  segment: EncodedSegment\n): string {\n  // Aside from being filesystem safe, segment keys are also designed so that\n  // each segment and parallel route creates its own subdirectory. Roughly in\n  // the same shape as the source app directory. This is mostly just for easier\n  // debugging (you can open up the build folder and navigate the output); if\n  // we wanted to do we could just use a flat structure.\n\n  // Omit the parallel route key for children, since this is the most\n  // common case. Saves some bytes (and it's what the app directory does).\n  const slotKey =\n    parallelRouteKey === 'children'\n      ? segment\n      : `@${encodeToFilesystemAndURLSafeString(parallelRouteKey)}/${segment}`\n\n  return parentSegmentKey + '/' + slotKey\n}\n\n// Define a regex pattern to match the most common characters found in a route\n// param. It excludes anything that might not be cross-platform filesystem\n// compatible, like |. It does not need to be precise because the fallback is to\n// just base64url-encode the whole parameter, which is fine; we just don't do it\n// by default for compactness, and for easier debugging.\nconst simpleParamValueRegex = /^[a-zA-Z0-9\\-_@]+$/\n\nfunction encodeToFilesystemAndURLSafeString(value: string) {\n  if (simpleParamValueRegex.test(value)) {\n    return value\n  }\n  // If there are any unsafe characters, base64url-encode the entire value.\n  // We also add a ! prefix so it doesn't collide with the simple case.\n  const base64url = btoa(value)\n    .replace(/\\+/g, '-') // Replace '+' with '-'\n    .replace(/\\//g, '_') // Replace '/' with '_'\n    .replace(/=+$/, '') // Remove trailing '='\n  return '!' + base64url\n}\n\nexport function convertSegmentPathToStaticExportFilename(\n  segmentPath: string\n): string {\n  return `__next${segmentPath.replace(/\\//g, '.')}.txt`\n}\n"], "names": ["ROOT_SEGMENT_KEY", "convertSegmentPathToStaticExportFilename", "encodeChildSegmentKey", "encodeSegment", "segment", "startsWith", "PAGE_SEGMENT_KEY", "safeName", "encodeToFilesystemAndURLSafeString", "name", "paramValue", "paramType", "safeValue", "encodedName", "parentSegmentKey", "parallelRouteKey", "<PERSON><PERSON><PERSON>", "simpleParamValueRegex", "value", "test", "base64url", "btoa", "replace", "segmentPath"], "mappings": ";;;;;;;;;;;;;;;;;IA6CaA,gBAAgB;eAAhBA;;IA4CGC,wCAAwC;eAAxCA;;IA1CAC,qBAAqB;eAArBA;;IAvCAC,aAAa;eAAbA;;;yBARiB;AAQ1B,SAASA,cACdC,OAAiC;IAEjC,IAAI,OAAOA,YAAY,UAAU;QAC/B,IAAIA,QAAQC,UAAU,CAACC,yBAAgB,GAAG;YACxC,uEAAuE;YACvE,yEAAyE;YACzE,wEAAwE;YACxE,sEAAsE;YACtE,uEAAuE;YACvE,yEAAyE;YACzE,4CAA4C;YAC5C,uEAAuE;YACvE,iEAAiE;YACjE,OAAOA,yBAAgB;QACzB;QACA,MAAMC,WACJ,qEAAqE;QACrE,sEAAsE;QACtE,mDAAmD;QACnDH,YAAY,gBACR,eACAI,mCAAmCJ;QACzC,uEAAuE;QACvE,4CAA4C;QAC5C,OAAOG;IACT;IACA,MAAME,OAAOL,OAAO,CAAC,EAAE;IACvB,MAAMM,aAAaN,OAAO,CAAC,EAAE;IAC7B,MAAMO,YAAYP,OAAO,CAAC,EAAE;IAC5B,MAAMG,WAAWC,mCAAmCC;IACpD,MAAMG,YAAYJ,mCAAmCE;IAErD,MAAMG,cAAc,MAAMF,YAAY,MAAMJ,WAAW,MAAMK;IAC7D,OAAOC;AACT;AAEO,MAAMb,mBAAmB;AAEzB,SAASE,sBACd,+CAA+C;AAC/CY,gBAAwB,EACxBC,gBAAwB,EACxBX,OAAuB;IAEvB,2EAA2E;IAC3E,2EAA2E;IAC3E,6EAA6E;IAC7E,2EAA2E;IAC3E,sDAAsD;IAEtD,mEAAmE;IACnE,wEAAwE;IACxE,MAAMY,UACJD,qBAAqB,aACjBX,UACA,AAAC,MAAGI,mCAAmCO,oBAAkB,MAAGX;IAElE,OAAOU,mBAAmB,MAAME;AAClC;AAEA,8EAA8E;AAC9E,0EAA0E;AAC1E,gFAAgF;AAChF,gFAAgF;AAChF,wDAAwD;AACxD,MAAMC,wBAAwB;AAE9B,SAAST,mCAAmCU,KAAa;IACvD,IAAID,sBAAsBE,IAAI,CAACD,QAAQ;QACrC,OAAOA;IACT;IACA,yEAAyE;IACzE,qEAAqE;IACrE,MAAME,YAAYC,KAAKH,OACpBI,OAAO,CAAC,OAAO,KAAK,uBAAuB;KAC3CA,OAAO,CAAC,OAAO,KAAK,uBAAuB;KAC3CA,OAAO,CAAC,OAAO,IAAI,sBAAsB;;IAC5C,OAAO,MAAMF;AACf;AAEO,SAASnB,yCACdsB,WAAmB;IAEnB,OAAO,AAAC,WAAQA,YAAYD,OAAO,CAAC,OAAO,OAAK;AAClD"}