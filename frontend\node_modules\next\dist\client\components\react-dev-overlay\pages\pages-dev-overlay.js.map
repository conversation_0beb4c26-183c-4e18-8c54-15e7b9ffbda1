{"version": 3, "sources": ["../../../../../src/client/components/react-dev-overlay/pages/pages-dev-overlay.tsx"], "sourcesContent": ["import { useState } from 'react'\nimport { PagesDevOverlayErrorBoundary } from './pages-dev-overlay-error-boundary'\nimport { usePagesDevOverlay } from './hooks'\nimport { FontStyles } from '../font/font-styles'\nimport { DevOverlay } from '../ui/dev-overlay'\n\nexport type ErrorType = 'runtime' | 'build'\n\nexport type PagesDevOverlayType = typeof PagesDevOverlay\n\ninterface PagesDevOverlayProps {\n  children?: React.ReactNode\n}\n\nexport function PagesDevOverlay({ children }: PagesDevOverlayProps) {\n  const { state, onComponentError } = usePagesDevOverlay()\n\n  const [isErrorOverlayOpen, setIsErrorOverlayOpen] = useState(true)\n\n  return (\n    <>\n      <PagesDevOverlayErrorBoundary onError={onComponentError}>\n        {children ?? null}\n      </PagesDevOverlayErrorBoundary>\n\n      {/* Fonts can only be loaded outside the Shadow DOM. */}\n      <FontStyles />\n      <DevOverlay\n        state={state}\n        isErrorOverlayOpen={isErrorOverlayOpen}\n        setIsErrorOverlayOpen={setIsErrorOverlayOpen}\n      />\n    </>\n  )\n}\n"], "names": ["<PERSON>s<PERSON><PERSON><PERSON><PERSON><PERSON>", "children", "state", "onComponentError", "usePagesDevOverlay", "isErrorOverlayOpen", "setIsErrorOverlayOpen", "useState", "PagesDevOverlayErrorBoundary", "onError", "FontStyles", "DevOverlay"], "mappings": ";;;;+BAcgBA;;;eAAAA;;;;uBAdS;8CACoB;uBACV;4BACR;4BACA;AAUpB,SAASA,gBAAgB,KAAkC;IAAlC,IAAA,EAAEC,QAAQ,EAAwB,GAAlC;IAC9B,MAAM,EAAEC,KAAK,EAAEC,gBAAgB,EAAE,GAAGC,IAAAA,yBAAkB;IAEtD,MAAM,CAACC,oBAAoBC,sBAAsB,GAAGC,IAAAA,eAAQ,EAAC;IAE7D,qBACE;;0BACE,qBAACC,0DAA4B;gBAACC,SAASN;0BACpCF,mBAAAA,WAAY;;0BAIf,qBAACS,sBAAU;0BACX,qBAACC,sBAAU;gBACTT,OAAOA;gBACPG,oBAAoBA;gBACpBC,uBAAuBA;;;;AAI/B"}