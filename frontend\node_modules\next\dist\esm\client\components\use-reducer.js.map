{"version": 3, "sources": ["../../../src/client/components/use-reducer.ts"], "sourcesContent": ["import type { Dispatch } from 'react'\nimport React, { use, useCallback } from 'react'\nimport { isThenable } from '../../shared/lib/is-thenable'\nimport type { AppRouterActionQueue } from '../../shared/lib/router/action-queue'\nimport type {\n  AppRouterState,\n  ReducerActions,\n  ReducerState,\n} from './router-reducer/router-reducer-types'\nimport { useSyncDevRenderIndicator } from './react-dev-overlay/utils/dev-indicator/use-sync-dev-render-indicator'\n\nexport function useUnwrapState(state: ReducerState): AppRouterState {\n  // reducer actions can be async, so sometimes we need to suspend until the state is resolved\n  if (isThenable(state)) {\n    const result = use(state)\n    return result\n  }\n\n  return state\n}\n\nexport function useReducer(\n  actionQueue: AppRouterActionQueue\n): [ReducerState, Dispatch<ReducerActions>] {\n  const [state, setState] = React.useState<ReducerState>(actionQueue.state)\n  const syncDevRenderIndicator = useSyncDevRenderIndicator()\n\n  const dispatch = useCallback(\n    (action: ReducerActions) => {\n      syncDevRenderIndicator(() => {\n        actionQueue.dispatch(action, setState)\n      })\n    },\n    [actionQueue, syncDevRenderIndicator]\n  )\n\n  return [state, dispatch]\n}\n"], "names": ["React", "use", "useCallback", "isThenable", "useSyncDevRenderIndicator", "useUnwrapState", "state", "result", "useReducer", "actionQueue", "setState", "useState", "syncDevRenderIndicator", "dispatch", "action"], "mappings": "AACA,OAAOA,SAASC,GAAG,EAAEC,WAAW,QAAQ,QAAO;AAC/C,SAASC,UAAU,QAAQ,+BAA8B;AAOzD,SAASC,yBAAyB,QAAQ,wEAAuE;AAEjH,OAAO,SAASC,eAAeC,KAAmB;IAChD,4FAA4F;IAC5F,IAAIH,WAAWG,QAAQ;QACrB,MAAMC,SAASN,IAAIK;QACnB,OAAOC;IACT;IAEA,OAAOD;AACT;AAEA,OAAO,SAASE,WACdC,WAAiC;IAEjC,MAAM,CAACH,OAAOI,SAAS,GAAGV,MAAMW,QAAQ,CAAeF,YAAYH,KAAK;IACxE,MAAMM,yBAAyBR;IAE/B,MAAMS,WAAWX,YACf,CAACY;QACCF,uBAAuB;YACrBH,YAAYI,QAAQ,CAACC,QAAQJ;QAC/B;IACF,GACA;QAACD;QAAaG;KAAuB;IAGvC,OAAO;QAACN;QAAOO;KAAS;AAC1B"}