{"version": 3, "sources": ["../../src/server/web-server.ts"], "sourcesContent": ["import type { WebNextRequest, WebNextResponse } from './base-http/web'\nimport type RenderResult from './render-result'\nimport type { NextParsedUrlQuery, NextUrlWithParsedQuery } from './request-meta'\nimport type { Params } from './request/params'\nimport type { LoadComponentsReturnType } from './load-components'\nimport type {\n  LoadedRenderOpts,\n  MiddlewareRoutingItem,\n  NormalizedRouteManifest,\n  Options,\n  RouteHandler,\n} from './base-server'\nimport type { CacheControl } from './lib/cache-control'\n\nimport { byteLength } from './api-utils/web'\nimport BaseServer, { NoFallbackError } from './base-server'\nimport { generateETag } from './lib/etag'\nimport { addRequestMeta, getRequestMeta } from './request-meta'\nimport WebResponseCache from './response-cache/web'\nimport { removeTrailingSlash } from '../shared/lib/router/utils/remove-trailing-slash'\nimport { isDynamicRoute } from '../shared/lib/router/utils'\nimport {\n  interpolateDynamicPath,\n  normalizeVercelUrl,\n  normalizeDynamicRouteParams,\n} from './server-utils'\nimport { getNamedRouteRegex } from '../shared/lib/router/utils/route-regex'\nimport { getRouteMatcher } from '../shared/lib/router/utils/route-matcher'\nimport { IncrementalCache } from './lib/incremental-cache'\nimport type { PAGE_TYPES } from '../lib/page-types'\nimport type { Rewrite } from '../lib/load-custom-routes'\nimport { buildCustomRoute } from '../lib/build-custom-route'\nimport { UNDERSCORE_NOT_FOUND_ROUTE } from '../api/constants'\nimport { getEdgeInstrumentationModule } from './web/globals'\nimport type { ServerOnInstrumentationRequestError } from './app-render/types'\nimport { getEdgePreviewProps } from './web/get-edge-preview-props'\n\ninterface WebServerOptions extends Options {\n  buildId: string\n  webServerConfig: {\n    page: string\n    pathname: string\n    pagesType: PAGE_TYPES\n    loadComponent: (page: string) => Promise<LoadComponentsReturnType | null>\n    extendRenderOpts: Partial<BaseServer['renderOpts']> & {\n      serverActionsManifest?: any\n    }\n    renderToHTML:\n      | typeof import('./app-render/app-render').renderToHTMLOrFlight\n      | undefined\n    incrementalCacheHandler?: any\n    interceptionRouteRewrites?: Rewrite[]\n  }\n}\n\ntype WebRouteHandler = RouteHandler<WebNextRequest, WebNextResponse>\n\nexport default class NextWebServer extends BaseServer<\n  WebServerOptions,\n  WebNextRequest,\n  WebNextResponse\n> {\n  constructor(options: WebServerOptions) {\n    super(options)\n\n    // Extend `renderOpts`.\n    Object.assign(this.renderOpts, options.webServerConfig.extendRenderOpts)\n  }\n\n  protected async getIncrementalCache({\n    requestHeaders,\n  }: {\n    requestHeaders: IncrementalCache['requestHeaders']\n  }) {\n    const dev = !!this.renderOpts.dev\n    // incremental-cache is request specific\n    // although can have shared caches in module scope\n    // per-cache handler\n    return new IncrementalCache({\n      dev,\n      requestHeaders,\n      requestProtocol: 'https',\n      allowedRevalidateHeaderKeys:\n        this.nextConfig.experimental.allowedRevalidateHeaderKeys,\n      minimalMode: this.minimalMode,\n      fetchCacheKeyPrefix: this.nextConfig.experimental.fetchCacheKeyPrefix,\n      maxMemoryCacheSize: this.nextConfig.cacheMaxMemorySize,\n      flushToDisk: false,\n      CurCacheHandler:\n        this.serverOptions.webServerConfig.incrementalCacheHandler,\n      getPrerenderManifest: () => this.getPrerenderManifest(),\n    })\n  }\n  protected getResponseCache() {\n    return new WebResponseCache(this.minimalMode)\n  }\n\n  protected async hasPage(page: string) {\n    return page === this.serverOptions.webServerConfig.page\n  }\n\n  protected getBuildId() {\n    return this.serverOptions.buildId\n  }\n\n  protected getEnabledDirectories() {\n    return {\n      app: this.serverOptions.webServerConfig.pagesType === 'app',\n      pages: this.serverOptions.webServerConfig.pagesType === 'pages',\n    }\n  }\n\n  protected getPagesManifest() {\n    return {\n      // keep same theme but server path doesn't need to be accurate\n      [this.serverOptions.webServerConfig.pathname]:\n        `server${this.serverOptions.webServerConfig.page}.js`,\n    }\n  }\n\n  protected getAppPathsManifest() {\n    const page = this.serverOptions.webServerConfig.page\n    return {\n      [this.serverOptions.webServerConfig.page]: `app${page}.js`,\n    }\n  }\n\n  protected attachRequestMeta(\n    req: WebNextRequest,\n    parsedUrl: NextUrlWithParsedQuery\n  ) {\n    addRequestMeta(req, 'initQuery', { ...parsedUrl.query })\n  }\n\n  protected getPrerenderManifest() {\n    return {\n      version: -1 as any, // letting us know this doesn't conform to spec\n      routes: {},\n      dynamicRoutes: {},\n      notFoundRoutes: [],\n      preview: getEdgePreviewProps(),\n    }\n  }\n\n  protected getNextFontManifest() {\n    return this.serverOptions.webServerConfig.extendRenderOpts.nextFontManifest\n  }\n\n  protected handleCatchallRenderRequest: WebRouteHandler = async (\n    req,\n    res,\n    parsedUrl\n  ) => {\n    let { pathname, query } = parsedUrl\n    if (!pathname) {\n      throw new Error('pathname is undefined')\n    }\n\n    // interpolate query information into page for dynamic route\n    // so that rewritten paths are handled properly\n    const normalizedPage = this.serverOptions.webServerConfig.pathname\n\n    if (pathname !== normalizedPage) {\n      pathname = normalizedPage\n\n      if (isDynamicRoute(pathname)) {\n        const routeRegex = getNamedRouteRegex(pathname, {\n          prefixRouteKeys: false,\n        })\n        const dynamicRouteMatcher = getRouteMatcher(routeRegex)\n        const defaultRouteMatches = dynamicRouteMatcher(\n          pathname\n        ) as NextParsedUrlQuery\n        const paramsResult = normalizeDynamicRouteParams(\n          query,\n          routeRegex,\n          defaultRouteMatches,\n          false\n        )\n        const normalizedParams = paramsResult.hasValidParams\n          ? paramsResult.params\n          : query\n\n        pathname = interpolateDynamicPath(\n          pathname,\n          normalizedParams,\n          routeRegex\n        )\n        normalizeVercelUrl(req, Object.keys(routeRegex.routeKeys), routeRegex)\n      }\n    }\n\n    // next.js core assumes page path without trailing slash\n    pathname = removeTrailingSlash(pathname)\n\n    if (this.i18nProvider) {\n      const { detectedLocale } = await this.i18nProvider.analyze(pathname)\n      if (detectedLocale) {\n        addRequestMeta(req, 'locale', detectedLocale)\n      }\n    }\n\n    const bubbleNoFallback = getRequestMeta(req, 'bubbleNoFallback')\n\n    try {\n      await this.render(req, res, pathname, query, parsedUrl, true)\n\n      return true\n    } catch (err) {\n      if (err instanceof NoFallbackError && bubbleNoFallback) {\n        return false\n      }\n      throw err\n    }\n  }\n\n  protected renderHTML(\n    req: WebNextRequest,\n    res: WebNextResponse,\n    pathname: string,\n    query: NextParsedUrlQuery,\n    renderOpts: LoadedRenderOpts\n  ): Promise<RenderResult> {\n    const { renderToHTML } = this.serverOptions.webServerConfig\n    if (!renderToHTML) {\n      throw new Error(\n        'Invariant: routeModule should be configured when rendering pages'\n      )\n    }\n\n    // For edge runtime if the pathname hit as /_not-found entrypoint,\n    // override the pathname to /404 for rendering\n    if (pathname === UNDERSCORE_NOT_FOUND_ROUTE) {\n      pathname = '/404'\n    }\n    return renderToHTML(\n      req as any,\n      res as any,\n      pathname,\n      query,\n      // Edge runtime does not support ISR/PPR, so we don't need to pass in\n      // the unknown params.\n      null,\n      Object.assign(renderOpts, {\n        disableOptimizedLoading: true,\n        runtime: 'experimental-edge',\n      }),\n      undefined,\n      false,\n      {\n        buildId: this.serverOptions.buildId,\n      }\n    )\n  }\n\n  protected async sendRenderResult(\n    _req: WebNextRequest,\n    res: WebNextResponse,\n    options: {\n      result: RenderResult\n      type: 'html' | 'json'\n      generateEtags: boolean\n      poweredByHeader: boolean\n      cacheControl: CacheControl | undefined\n    }\n  ): Promise<void> {\n    res.setHeader('X-Edge-Runtime', '1')\n\n    // Add necessary headers.\n    // @TODO: Share the isomorphic logic with server/send-payload.ts.\n    if (options.poweredByHeader && options.type === 'html') {\n      res.setHeader('X-Powered-By', 'Next.js')\n    }\n\n    if (!res.getHeader('Content-Type')) {\n      res.setHeader(\n        'Content-Type',\n        options.result.contentType\n          ? options.result.contentType\n          : options.type === 'json'\n            ? 'application/json'\n            : 'text/html; charset=utf-8'\n      )\n    }\n\n    let promise: Promise<void> | undefined\n    if (options.result.isDynamic) {\n      promise = options.result.pipeTo(res.transformStream.writable)\n    } else {\n      const payload = options.result.toUnchunkedString()\n      res.setHeader('Content-Length', String(byteLength(payload)))\n      if (options.generateEtags) {\n        res.setHeader('ETag', generateETag(payload))\n      }\n      res.body(payload)\n    }\n\n    res.send()\n\n    // If we have a promise, wait for it to resolve.\n    if (promise) await promise\n  }\n\n  protected async findPageComponents({\n    page,\n    query,\n    params,\n    url: _url,\n  }: {\n    page: string\n    query: NextParsedUrlQuery\n    params: Params | null\n    isAppPath: boolean\n    url?: string\n  }) {\n    const result = await this.serverOptions.webServerConfig.loadComponent(page)\n    if (!result) return null\n\n    return {\n      query: {\n        ...(query || {}),\n        ...(params || {}),\n      },\n      components: result,\n    }\n  }\n\n  // Below are methods that are not implemented by the web server as they are\n  // handled by the upstream proxy (edge runtime or node server).\n\n  protected async runApi() {\n    // This web server does not need to handle API requests.\n    return true\n  }\n\n  protected async handleApiRequest() {\n    // Edge API requests are handled separately in minimal mode.\n    return false\n  }\n\n  protected loadEnvConfig() {\n    // The web server does not need to load the env config. This is done by the\n    // runtime already.\n  }\n\n  protected getPublicDir() {\n    // Public files are not handled by the web server.\n    return ''\n  }\n\n  protected getHasStaticDir() {\n    return false\n  }\n\n  protected getFontManifest() {\n    return undefined\n  }\n\n  protected handleCompression() {\n    // For the web server layer, compression is automatically handled by the\n    // upstream proxy (edge runtime or node server) and we can simply skip here.\n  }\n\n  protected async handleUpgrade(): Promise<void> {\n    // The web server does not support web sockets.\n  }\n\n  protected async getFallbackErrorComponents(\n    _url?: string\n  ): Promise<LoadComponentsReturnType | null> {\n    // The web server does not need to handle fallback errors in production.\n    return null\n  }\n  protected getRoutesManifest(): NormalizedRouteManifest | undefined {\n    // The web server does not need to handle rewrite rules. This is done by the\n    // upstream proxy (edge runtime or node server).\n    return undefined\n  }\n\n  protected getMiddleware(): Promise<MiddlewareRoutingItem | undefined> {\n    // The web server does not need to handle middleware. This is done by the\n    // upstream proxy (edge runtime or node server).\n    return Promise.resolve(undefined)\n  }\n\n  protected getFilesystemPaths() {\n    return new Set<string>()\n  }\n\n  protected getinterceptionRoutePatterns(): RegExp[] {\n    return (\n      this.serverOptions.webServerConfig.interceptionRouteRewrites?.map(\n        (rewrite) => new RegExp(buildCustomRoute('rewrite', rewrite).regex)\n      ) ?? []\n    )\n  }\n\n  protected async loadInstrumentationModule() {\n    return await getEdgeInstrumentationModule()\n  }\n\n  protected async instrumentationOnRequestError(\n    ...args: Parameters<ServerOnInstrumentationRequestError>\n  ) {\n    await super.instrumentationOnRequestError(...args)\n    const err = args[0]\n\n    if (\n      process.env.NODE_ENV !== 'production' &&\n      typeof __next_log_error__ === 'function'\n    ) {\n      __next_log_error__(err)\n    } else {\n      console.error(err)\n    }\n  }\n}\n"], "names": ["byteLength", "BaseServer", "NoFallbackError", "generateETag", "addRequestMeta", "getRequestMeta", "WebResponseCache", "removeTrailingSlash", "isDynamicRoute", "interpolateDynamicPath", "normalizeVercelUrl", "normalizeDynamicRouteParams", "getNamedRouteRegex", "getRouteMatcher", "IncrementalCache", "buildCustomRoute", "UNDERSCORE_NOT_FOUND_ROUTE", "getEdgeInstrumentationModule", "getEdgePreviewProps", "NextWebServer", "constructor", "options", "handleCatchallRenderRequest", "req", "res", "parsedUrl", "pathname", "query", "Error", "normalizedPage", "serverOptions", "webServerConfig", "routeRegex", "prefixRouteKeys", "dynamicRouteMatcher", "defaultRouteMatches", "paramsResult", "normalizedParams", "hasValidParams", "params", "Object", "keys", "routeKeys", "i18nProvider", "detectedLocale", "analyze", "bubbleNoFallback", "render", "err", "assign", "renderOpts", "extendRenderOpts", "getIncrementalCache", "requestHeaders", "dev", "requestProtocol", "allowedRevalidateHeaderKeys", "nextConfig", "experimental", "minimalMode", "fetchCacheKeyPrefix", "maxMemoryCacheSize", "cacheMaxMemorySize", "flushToDisk", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "incremental<PERSON>ache<PERSON><PERSON><PERSON>", "getPrerenderManifest", "getResponseCache", "hasPage", "page", "getBuildId", "buildId", "getEnabledDirectories", "app", "pagesType", "pages", "getPagesManifest", "getAppPathsManifest", "attachRequestMeta", "version", "routes", "dynamicRoutes", "notFoundRoutes", "preview", "getNextFontManifest", "nextFontManifest", "renderHTML", "renderToHTML", "disableOptimizedLoading", "runtime", "undefined", "sendRenderResult", "_req", "<PERSON><PERSON><PERSON><PERSON>", "poweredByHeader", "type", "<PERSON><PERSON><PERSON><PERSON>", "result", "contentType", "promise", "isDynamic", "pipeTo", "transformStream", "writable", "payload", "toUnchunkedString", "String", "generateEtags", "body", "send", "findPageComponents", "url", "_url", "loadComponent", "components", "run<PERSON><PERSON>", "handleApiRequest", "loadEnvConfig", "getPublicDir", "getHasStaticDir", "getFontManifest", "handleCompression", "handleUpgrade", "getFallbackErrorComponents", "getRoutesManifest", "getMiddleware", "Promise", "resolve", "getFilesystemPaths", "Set", "getinterceptionRoutePatterns", "interceptionRouteRewrites", "map", "rewrite", "RegExp", "regex", "loadInstrumentationModule", "instrumentationOnRequestError", "args", "process", "env", "NODE_ENV", "__next_log_error__", "console", "error"], "mappings": "AAcA,SAASA,UAAU,QAAQ,kBAAiB;AAC5C,OAAOC,cAAcC,eAAe,QAAQ,gBAAe;AAC3D,SAASC,YAAY,QAAQ,aAAY;AACzC,SAASC,cAAc,EAAEC,cAAc,QAAQ,iBAAgB;AAC/D,OAAOC,sBAAsB,uBAAsB;AACnD,SAASC,mBAAmB,QAAQ,mDAAkD;AACtF,SAASC,cAAc,QAAQ,6BAA4B;AAC3D,SACEC,sBAAsB,EACtBC,kBAAkB,EAClBC,2BAA2B,QACtB,iBAAgB;AACvB,SAASC,kBAAkB,QAAQ,yCAAwC;AAC3E,SAASC,eAAe,QAAQ,2CAA0C;AAC1E,SAASC,gBAAgB,QAAQ,0BAAyB;AAG1D,SAASC,gBAAgB,QAAQ,4BAA2B;AAC5D,SAASC,0BAA0B,QAAQ,mBAAkB;AAC7D,SAASC,4BAA4B,QAAQ,gBAAe;AAE5D,SAASC,mBAAmB,QAAQ,+BAA8B;AAsBlE,eAAe,MAAMC,sBAAsBlB;IAKzCmB,YAAYC,OAAyB,CAAE;QACrC,KAAK,CAACA,eAqFEC,8BAA+C,OACvDC,KACAC,KACAC;YAEA,IAAI,EAAEC,QAAQ,EAAEC,KAAK,EAAE,GAAGF;YAC1B,IAAI,CAACC,UAAU;gBACb,MAAM,qBAAkC,CAAlC,IAAIE,MAAM,0BAAV,qBAAA;2BAAA;gCAAA;kCAAA;gBAAiC;YACzC;YAEA,4DAA4D;YAC5D,+CAA+C;YAC/C,MAAMC,iBAAiB,IAAI,CAACC,aAAa,CAACC,eAAe,CAACL,QAAQ;YAElE,IAAIA,aAAaG,gBAAgB;gBAC/BH,WAAWG;gBAEX,IAAIrB,eAAekB,WAAW;oBAC5B,MAAMM,aAAapB,mBAAmBc,UAAU;wBAC9CO,iBAAiB;oBACnB;oBACA,MAAMC,sBAAsBrB,gBAAgBmB;oBAC5C,MAAMG,sBAAsBD,oBAC1BR;oBAEF,MAAMU,eAAezB,4BACnBgB,OACAK,YACAG,qBACA;oBAEF,MAAME,mBAAmBD,aAAaE,cAAc,GAChDF,aAAaG,MAAM,GACnBZ;oBAEJD,WAAWjB,uBACTiB,UACAW,kBACAL;oBAEFtB,mBAAmBa,KAAKiB,OAAOC,IAAI,CAACT,WAAWU,SAAS,GAAGV;gBAC7D;YACF;YAEA,wDAAwD;YACxDN,WAAWnB,oBAAoBmB;YAE/B,IAAI,IAAI,CAACiB,YAAY,EAAE;gBACrB,MAAM,EAAEC,cAAc,EAAE,GAAG,MAAM,IAAI,CAACD,YAAY,CAACE,OAAO,CAACnB;gBAC3D,IAAIkB,gBAAgB;oBAClBxC,eAAemB,KAAK,UAAUqB;gBAChC;YACF;YAEA,MAAME,mBAAmBzC,eAAekB,KAAK;YAE7C,IAAI;gBACF,MAAM,IAAI,CAACwB,MAAM,CAACxB,KAAKC,KAAKE,UAAUC,OAAOF,WAAW;gBAExD,OAAO;YACT,EAAE,OAAOuB,KAAK;gBACZ,IAAIA,eAAe9C,mBAAmB4C,kBAAkB;oBACtD,OAAO;gBACT;gBACA,MAAME;YACR;QACF;QArJE,uBAAuB;QACvBR,OAAOS,MAAM,CAAC,IAAI,CAACC,UAAU,EAAE7B,QAAQU,eAAe,CAACoB,gBAAgB;IACzE;IAEA,MAAgBC,oBAAoB,EAClCC,cAAc,EAGf,EAAE;QACD,MAAMC,MAAM,CAAC,CAAC,IAAI,CAACJ,UAAU,CAACI,GAAG;QACjC,wCAAwC;QACxC,kDAAkD;QAClD,oBAAoB;QACpB,OAAO,IAAIxC,iBAAiB;YAC1BwC;YACAD;YACAE,iBAAiB;YACjBC,6BACE,IAAI,CAACC,UAAU,CAACC,YAAY,CAACF,2BAA2B;YAC1DG,aAAa,IAAI,CAACA,WAAW;YAC7BC,qBAAqB,IAAI,CAACH,UAAU,CAACC,YAAY,CAACE,mBAAmB;YACrEC,oBAAoB,IAAI,CAACJ,UAAU,CAACK,kBAAkB;YACtDC,aAAa;YACbC,iBACE,IAAI,CAAClC,aAAa,CAACC,eAAe,CAACkC,uBAAuB;YAC5DC,sBAAsB,IAAM,IAAI,CAACA,oBAAoB;QACvD;IACF;IACUC,mBAAmB;QAC3B,OAAO,IAAI7D,iBAAiB,IAAI,CAACqD,WAAW;IAC9C;IAEA,MAAgBS,QAAQC,IAAY,EAAE;QACpC,OAAOA,SAAS,IAAI,CAACvC,aAAa,CAACC,eAAe,CAACsC,IAAI;IACzD;IAEUC,aAAa;QACrB,OAAO,IAAI,CAACxC,aAAa,CAACyC,OAAO;IACnC;IAEUC,wBAAwB;QAChC,OAAO;YACLC,KAAK,IAAI,CAAC3C,aAAa,CAACC,eAAe,CAAC2C,SAAS,KAAK;YACtDC,OAAO,IAAI,CAAC7C,aAAa,CAACC,eAAe,CAAC2C,SAAS,KAAK;QAC1D;IACF;IAEUE,mBAAmB;QAC3B,OAAO;YACL,8DAA8D;YAC9D,CAAC,IAAI,CAAC9C,aAAa,CAACC,eAAe,CAACL,QAAQ,CAAC,EAC3C,CAAC,MAAM,EAAE,IAAI,CAACI,aAAa,CAACC,eAAe,CAACsC,IAAI,CAAC,GAAG,CAAC;QACzD;IACF;IAEUQ,sBAAsB;QAC9B,MAAMR,OAAO,IAAI,CAACvC,aAAa,CAACC,eAAe,CAACsC,IAAI;QACpD,OAAO;YACL,CAAC,IAAI,CAACvC,aAAa,CAACC,eAAe,CAACsC,IAAI,CAAC,EAAE,CAAC,GAAG,EAAEA,KAAK,GAAG,CAAC;QAC5D;IACF;IAEUS,kBACRvD,GAAmB,EACnBE,SAAiC,EACjC;QACArB,eAAemB,KAAK,aAAa;YAAE,GAAGE,UAAUE,KAAK;QAAC;IACxD;IAEUuC,uBAAuB;QAC/B,OAAO;YACLa,SAAS,CAAC;YACVC,QAAQ,CAAC;YACTC,eAAe,CAAC;YAChBC,gBAAgB,EAAE;YAClBC,SAASjE;QACX;IACF;IAEUkE,sBAAsB;QAC9B,OAAO,IAAI,CAACtD,aAAa,CAACC,eAAe,CAACoB,gBAAgB,CAACkC,gBAAgB;IAC7E;IAsEUC,WACR/D,GAAmB,EACnBC,GAAoB,EACpBE,QAAgB,EAChBC,KAAyB,EACzBuB,UAA4B,EACL;QACvB,MAAM,EAAEqC,YAAY,EAAE,GAAG,IAAI,CAACzD,aAAa,CAACC,eAAe;QAC3D,IAAI,CAACwD,cAAc;YACjB,MAAM,qBAEL,CAFK,IAAI3D,MACR,qEADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,kEAAkE;QAClE,8CAA8C;QAC9C,IAAIF,aAAaV,4BAA4B;YAC3CU,WAAW;QACb;QACA,OAAO6D,aACLhE,KACAC,KACAE,UACAC,OACA,qEAAqE;QACrE,sBAAsB;QACtB,MACAa,OAAOS,MAAM,CAACC,YAAY;YACxBsC,yBAAyB;YACzBC,SAAS;QACX,IACAC,WACA,OACA;YACEnB,SAAS,IAAI,CAACzC,aAAa,CAACyC,OAAO;QACrC;IAEJ;IAEA,MAAgBoB,iBACdC,IAAoB,EACpBpE,GAAoB,EACpBH,OAMC,EACc;QACfG,IAAIqE,SAAS,CAAC,kBAAkB;QAEhC,yBAAyB;QACzB,iEAAiE;QACjE,IAAIxE,QAAQyE,eAAe,IAAIzE,QAAQ0E,IAAI,KAAK,QAAQ;YACtDvE,IAAIqE,SAAS,CAAC,gBAAgB;QAChC;QAEA,IAAI,CAACrE,IAAIwE,SAAS,CAAC,iBAAiB;YAClCxE,IAAIqE,SAAS,CACX,gBACAxE,QAAQ4E,MAAM,CAACC,WAAW,GACtB7E,QAAQ4E,MAAM,CAACC,WAAW,GAC1B7E,QAAQ0E,IAAI,KAAK,SACf,qBACA;QAEV;QAEA,IAAII;QACJ,IAAI9E,QAAQ4E,MAAM,CAACG,SAAS,EAAE;YAC5BD,UAAU9E,QAAQ4E,MAAM,CAACI,MAAM,CAAC7E,IAAI8E,eAAe,CAACC,QAAQ;QAC9D,OAAO;YACL,MAAMC,UAAUnF,QAAQ4E,MAAM,CAACQ,iBAAiB;YAChDjF,IAAIqE,SAAS,CAAC,kBAAkBa,OAAO1G,WAAWwG;YAClD,IAAInF,QAAQsF,aAAa,EAAE;gBACzBnF,IAAIqE,SAAS,CAAC,QAAQ1F,aAAaqG;YACrC;YACAhF,IAAIoF,IAAI,CAACJ;QACX;QAEAhF,IAAIqF,IAAI;QAER,gDAAgD;QAChD,IAAIV,SAAS,MAAMA;IACrB;IAEA,MAAgBW,mBAAmB,EACjCzC,IAAI,EACJ1C,KAAK,EACLY,MAAM,EACNwE,KAAKC,IAAI,EAOV,EAAE;QACD,MAAMf,SAAS,MAAM,IAAI,CAACnE,aAAa,CAACC,eAAe,CAACkF,aAAa,CAAC5C;QACtE,IAAI,CAAC4B,QAAQ,OAAO;QAEpB,OAAO;YACLtE,OAAO;gBACL,GAAIA,SAAS,CAAC,CAAC;gBACf,GAAIY,UAAU,CAAC,CAAC;YAClB;YACA2E,YAAYjB;QACd;IACF;IAEA,2EAA2E;IAC3E,+DAA+D;IAE/D,MAAgBkB,SAAS;QACvB,wDAAwD;QACxD,OAAO;IACT;IAEA,MAAgBC,mBAAmB;QACjC,4DAA4D;QAC5D,OAAO;IACT;IAEUC,gBAAgB;IACxB,2EAA2E;IAC3E,mBAAmB;IACrB;IAEUC,eAAe;QACvB,kDAAkD;QAClD,OAAO;IACT;IAEUC,kBAAkB;QAC1B,OAAO;IACT;IAEUC,kBAAkB;QAC1B,OAAO9B;IACT;IAEU+B,oBAAoB;IAC5B,wEAAwE;IACxE,4EAA4E;IAC9E;IAEA,MAAgBC,gBAA+B;IAC7C,+CAA+C;IACjD;IAEA,MAAgBC,2BACdX,IAAa,EAC6B;QAC1C,wEAAwE;QACxE,OAAO;IACT;IACUY,oBAAyD;QACjE,4EAA4E;QAC5E,gDAAgD;QAChD,OAAOlC;IACT;IAEUmC,gBAA4D;QACpE,yEAAyE;QACzE,gDAAgD;QAChD,OAAOC,QAAQC,OAAO,CAACrC;IACzB;IAEUsC,qBAAqB;QAC7B,OAAO,IAAIC;IACb;IAEUC,+BAAyC;YAE/C;QADF,OACE,EAAA,gEAAA,IAAI,CAACpG,aAAa,CAACC,eAAe,CAACoG,yBAAyB,qBAA5D,8DAA8DC,GAAG,CAC/D,CAACC,UAAY,IAAIC,OAAOvH,iBAAiB,WAAWsH,SAASE,KAAK,OAC/D,EAAE;IAEX;IAEA,MAAgBC,4BAA4B;QAC1C,OAAO,MAAMvH;IACf;IAEA,MAAgBwH,8BACd,GAAGC,IAAqD,EACxD;QACA,MAAM,KAAK,CAACD,iCAAiCC;QAC7C,MAAM1F,MAAM0F,IAAI,CAAC,EAAE;QAEnB,IACEC,QAAQC,GAAG,CAACC,QAAQ,KAAK,gBACzB,OAAOC,uBAAuB,YAC9B;YACAA,mBAAmB9F;QACrB,OAAO;YACL+F,QAAQC,KAAK,CAAChG;QAChB;IACF;AACF"}