{"version": 3, "sources": ["../../../../src/server/web/sandbox/sandbox.ts"], "sourcesContent": ["import type { NodejsRequestData, FetchEventResult, RequestData } from '../types'\nimport type { EdgeFunctionDefinition } from '../../../build/webpack/plugins/middleware-plugin'\nimport type { EdgeRuntime } from 'next/dist/compiled/edge-runtime'\nimport {\n  getModuleContext,\n  requestStore,\n  edgeSandboxNextRequestContext,\n} from './context'\nimport { requestToBodyStream } from '../../body-streams'\nimport { NEXT_RSC_UNION_QUERY } from '../../../client/components/app-router-headers'\nimport type { ServerComponentsHmrCache } from '../../response-cache'\nimport {\n  getBuiltinRequestContext,\n  type BuiltinRequestContextValue,\n} from '../../after/builtin-request-context'\n\nexport const ErrorSource = Symbol('SandboxError')\n\nconst FORBIDDEN_HEADERS = [\n  'content-length',\n  'content-encoding',\n  'transfer-encoding',\n]\n\ninterface RunnerFnParams {\n  name: string\n  onError?: (err: unknown) => void\n  onWarning?: (warn: Error) => void\n  paths: string[]\n  request: NodejsRequestData\n  useCache: boolean\n  edgeFunctionEntry: Pick<EdgeFunctionDefinition, 'assets' | 'wasm' | 'env'>\n  distDir: string\n  incrementalCache?: any\n  serverComponentsHmrCache?: ServerComponentsHmrCache\n}\n\ntype RunnerFn = (params: RunnerFnParams) => Promise<FetchEventResult>\n\n/**\n * Decorates the runner function making sure all errors it can produce are\n * tagged with `edge-server` so they can properly be rendered in dev.\n */\nfunction withTaggedErrors(fn: RunnerFn): RunnerFn {\n  if (process.env.NODE_ENV === 'development') {\n    const { getServerError } =\n      require('../../../client/components/react-dev-overlay/server/middleware-webpack') as typeof import('../../../client/components/react-dev-overlay/server/middleware-webpack')\n\n    return (params) =>\n      fn(params)\n        .then((result) => ({\n          ...result,\n          waitUntil: result?.waitUntil?.catch((error) => {\n            // TODO: used COMPILER_NAMES.edgeServer instead. Verify that it does not increase the runtime size.\n            throw getServerError(error, 'edge-server')\n          }),\n        }))\n        .catch((error) => {\n          // TODO: used COMPILER_NAMES.edgeServer instead\n          throw getServerError(error, 'edge-server')\n        })\n  }\n\n  return fn\n}\n\nexport async function getRuntimeContext(\n  params: Omit<RunnerFnParams, 'request'>\n): Promise<EdgeRuntime<any>> {\n  const { runtime, evaluateInContext } = await getModuleContext({\n    moduleName: params.name,\n    onWarning: params.onWarning ?? (() => {}),\n    onError: params.onError ?? (() => {}),\n    useCache: params.useCache !== false,\n    edgeFunctionEntry: params.edgeFunctionEntry,\n    distDir: params.distDir,\n  })\n\n  if (params.incrementalCache) {\n    runtime.context.globalThis.__incrementalCache = params.incrementalCache\n  }\n\n  if (params.serverComponentsHmrCache) {\n    runtime.context.globalThis.__serverComponentsHmrCache =\n      params.serverComponentsHmrCache\n  }\n\n  for (const paramPath of params.paths) {\n    evaluateInContext(paramPath)\n  }\n  return runtime\n}\n\nexport const run = withTaggedErrors(async function runWithTaggedErrors(params) {\n  const runtime = await getRuntimeContext(params)\n\n  const edgeFunction: (args: {\n    request: RequestData\n  }) => Promise<FetchEventResult> = (\n    await runtime.context._ENTRIES[`middleware_${params.name}`]\n  ).default\n\n  const cloned = !['HEAD', 'GET'].includes(params.request.method)\n    ? params.request.body?.cloneBodyStream()\n    : undefined\n\n  const KUint8Array = runtime.evaluate('Uint8Array')\n  const urlInstance = new URL(params.request.url)\n  urlInstance.searchParams.delete(NEXT_RSC_UNION_QUERY)\n\n  params.request.url = urlInstance.toString()\n\n  const headers = new Headers()\n  for (const [key, value] of Object.entries(params.request.headers)) {\n    headers.set(key, value?.toString() ?? '')\n  }\n\n  try {\n    let result: FetchEventResult | undefined = undefined\n    const builtinRequestCtx: BuiltinRequestContextValue = {\n      ...getBuiltinRequestContext(),\n      // FIXME(after):\n      // arguably, this is an abuse of \"@next/request-context\" --\n      // it'd make more sense to simply forward its existing value into the sandbox (in `createModuleContext`)\n      // but here we're using it to just pass in `waitUntil` regardless if we were running in this context or not.\n      waitUntil: params.request.waitUntil,\n    }\n    await edgeSandboxNextRequestContext.run(builtinRequestCtx, () =>\n      requestStore.run({ headers }, async () => {\n        result = await edgeFunction({\n          request: {\n            ...params.request,\n            body:\n              cloned &&\n              requestToBodyStream(runtime.context, KUint8Array, cloned),\n          },\n        })\n        for (const headerName of FORBIDDEN_HEADERS) {\n          result.response.headers.delete(headerName)\n        }\n      })\n    )\n\n    if (!result) throw new Error('Edge function did not return a response')\n    return result\n  } finally {\n    await params.request.body?.finalize()\n  }\n})\n"], "names": ["ErrorSource", "getRuntimeContext", "run", "Symbol", "FORBIDDEN_HEADERS", "withTaggedErrors", "fn", "process", "env", "NODE_ENV", "getServerError", "require", "params", "then", "result", "waitUntil", "catch", "error", "runtime", "evaluateInContext", "getModuleContext", "moduleName", "name", "onWarning", "onError", "useCache", "edgeFunctionEntry", "distDir", "incrementalCache", "context", "globalThis", "__incrementalCache", "serverComponentsHmrCache", "__serverComponentsHmrCache", "<PERSON><PERSON><PERSON><PERSON>", "paths", "runWithTaggedErrors", "edgeFunction", "_ENTRIES", "default", "cloned", "includes", "request", "method", "body", "cloneBodyStream", "undefined", "KUint8Array", "evaluate", "urlInstance", "URL", "url", "searchParams", "delete", "NEXT_RSC_UNION_QUERY", "toString", "headers", "Headers", "key", "value", "Object", "entries", "set", "builtinRequestCtx", "getBuiltinRequestContext", "edgeSandboxNextRequestContext", "requestStore", "requestToBodyStream", "headerName", "response", "Error", "finalize"], "mappings": ";;;;;;;;;;;;;;;;IAgBaA,WAAW;eAAXA;;IAkDSC,iBAAiB;eAAjBA;;IA2BTC,GAAG;eAAHA;;;yBAtFN;6BAC6B;kCACC;uCAK9B;AAEA,MAAMF,cAAcG,OAAO;AAElC,MAAMC,oBAAoB;IACxB;IACA;IACA;CACD;AAiBD;;;CAGC,GACD,SAASC,iBAAiBC,EAAY;IACpC,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;QAC1C,MAAM,EAAEC,cAAc,EAAE,GACtBC,QAAQ;QAEV,OAAO,CAACC,SACNN,GAAGM,QACAC,IAAI,CAAC,CAACC;oBAEMA;uBAFM;oBACjB,GAAGA,MAAM;oBACTC,SAAS,EAAED,2BAAAA,oBAAAA,OAAQC,SAAS,qBAAjBD,kBAAmBE,KAAK,CAAC,CAACC;wBACnC,mGAAmG;wBACnG,MAAMP,eAAeO,OAAO;oBAC9B;gBACF;eACCD,KAAK,CAAC,CAACC;gBACN,+CAA+C;gBAC/C,MAAMP,eAAeO,OAAO;YAC9B;IACN;IAEA,OAAOX;AACT;AAEO,eAAeL,kBACpBW,MAAuC;IAEvC,MAAM,EAAEM,OAAO,EAAEC,iBAAiB,EAAE,GAAG,MAAMC,IAAAA,yBAAgB,EAAC;QAC5DC,YAAYT,OAAOU,IAAI;QACvBC,WAAWX,OAAOW,SAAS,IAAK,CAAA,KAAO,CAAA;QACvCC,SAASZ,OAAOY,OAAO,IAAK,CAAA,KAAO,CAAA;QACnCC,UAAUb,OAAOa,QAAQ,KAAK;QAC9BC,mBAAmBd,OAAOc,iBAAiB;QAC3CC,SAASf,OAAOe,OAAO;IACzB;IAEA,IAAIf,OAAOgB,gBAAgB,EAAE;QAC3BV,QAAQW,OAAO,CAACC,UAAU,CAACC,kBAAkB,GAAGnB,OAAOgB,gBAAgB;IACzE;IAEA,IAAIhB,OAAOoB,wBAAwB,EAAE;QACnCd,QAAQW,OAAO,CAACC,UAAU,CAACG,0BAA0B,GACnDrB,OAAOoB,wBAAwB;IACnC;IAEA,KAAK,MAAME,aAAatB,OAAOuB,KAAK,CAAE;QACpChB,kBAAkBe;IACpB;IACA,OAAOhB;AACT;AAEO,MAAMhB,MAAMG,iBAAiB,eAAe+B,oBAAoBxB,MAAM;QAUvEA;IATJ,MAAMM,UAAU,MAAMjB,kBAAkBW;IAExC,MAAMyB,eAE4B,AAChC,CAAA,MAAMnB,QAAQW,OAAO,CAACS,QAAQ,CAAC,CAAC,WAAW,EAAE1B,OAAOU,IAAI,EAAE,CAAC,AAAD,EAC1DiB,OAAO;IAET,MAAMC,SAAS,CAAC;QAAC;QAAQ;KAAM,CAACC,QAAQ,CAAC7B,OAAO8B,OAAO,CAACC,MAAM,KAC1D/B,uBAAAA,OAAO8B,OAAO,CAACE,IAAI,qBAAnBhC,qBAAqBiC,eAAe,KACpCC;IAEJ,MAAMC,cAAc7B,QAAQ8B,QAAQ,CAAC;IACrC,MAAMC,cAAc,IAAIC,IAAItC,OAAO8B,OAAO,CAACS,GAAG;IAC9CF,YAAYG,YAAY,CAACC,MAAM,CAACC,sCAAoB;IAEpD1C,OAAO8B,OAAO,CAACS,GAAG,GAAGF,YAAYM,QAAQ;IAEzC,MAAMC,UAAU,IAAIC;IACpB,KAAK,MAAM,CAACC,KAAKC,MAAM,IAAIC,OAAOC,OAAO,CAACjD,OAAO8B,OAAO,CAACc,OAAO,EAAG;QACjEA,QAAQM,GAAG,CAACJ,KAAKC,CAAAA,yBAAAA,MAAOJ,QAAQ,OAAM;IACxC;IAEA,IAAI;QACF,IAAIzC,SAAuCgC;QAC3C,MAAMiB,oBAAgD;YACpD,GAAGC,IAAAA,+CAAwB,GAAE;YAC7B,gBAAgB;YAChB,2DAA2D;YAC3D,wGAAwG;YACxG,4GAA4G;YAC5GjD,WAAWH,OAAO8B,OAAO,CAAC3B,SAAS;QACrC;QACA,MAAMkD,sCAA6B,CAAC/D,GAAG,CAAC6D,mBAAmB,IACzDG,qBAAY,CAAChE,GAAG,CAAC;gBAAEsD;YAAQ,GAAG;gBAC5B1C,SAAS,MAAMuB,aAAa;oBAC1BK,SAAS;wBACP,GAAG9B,OAAO8B,OAAO;wBACjBE,MACEJ,UACA2B,IAAAA,gCAAmB,EAACjD,QAAQW,OAAO,EAAEkB,aAAaP;oBACtD;gBACF;gBACA,KAAK,MAAM4B,cAAchE,kBAAmB;oBAC1CU,OAAOuD,QAAQ,CAACb,OAAO,CAACH,MAAM,CAACe;gBACjC;YACF;QAGF,IAAI,CAACtD,QAAQ,MAAM,qBAAoD,CAApD,IAAIwD,MAAM,4CAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAmD;QACtE,OAAOxD;IACT,SAAU;YACFF;QAAN,QAAMA,wBAAAA,OAAO8B,OAAO,CAACE,IAAI,qBAAnBhC,sBAAqB2D,QAAQ;IACrC;AACF"}