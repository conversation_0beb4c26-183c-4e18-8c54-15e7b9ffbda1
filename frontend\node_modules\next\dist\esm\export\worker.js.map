{"version": 3, "sources": ["../../src/export/worker.ts"], "sourcesContent": ["import type {\n  ExportPagesInput,\n  ExportPageInput,\n  ExportPageResult,\n  ExportRouteResult,\n  WorkerRenderOpts,\n  ExportPagesResult,\n} from './types'\n\nimport '../server/node-environment'\n\nprocess.env.NEXT_IS_EXPORT_WORKER = 'true'\n\nimport { extname, join, dirname, sep } from 'path'\nimport fs from 'fs/promises'\nimport { loadComponents } from '../server/load-components'\nimport { isDynamicRoute } from '../shared/lib/router/utils/is-dynamic'\nimport { normalizePagePath } from '../shared/lib/page-path/normalize-page-path'\nimport { normalizeLocalePath } from '../shared/lib/i18n/normalize-locale-path'\nimport { trace } from '../trace'\nimport { setHttpClientAndAgentOptions } from '../server/setup-http-agent-env'\nimport isError from '../lib/is-error'\nimport { addRequestMeta } from '../server/request-meta'\nimport { normalizeAppPath } from '../shared/lib/router/utils/app-paths'\n\nimport { createRequestResponseMocks } from '../server/lib/mock-request'\nimport { isAppRouteRoute } from '../lib/is-app-route-route'\nimport { hasNextSupport } from '../server/ci-info'\nimport { exportAppRoute } from './routes/app-route'\nimport { exportAppPage, prospectiveRenderAppPage } from './routes/app-page'\nimport { exportPagesPage } from './routes/pages'\nimport { getParams } from './helpers/get-params'\nimport { createIncrementalCache } from './helpers/create-incremental-cache'\nimport { isPostpone } from '../server/lib/router-utils/is-postpone'\nimport { isDynamicUsageError } from './helpers/is-dynamic-usage-error'\nimport { isBailoutToCSRError } from '../shared/lib/lazy-dynamic/bailout-to-csr'\nimport {\n  turborepoTraceAccess,\n  TurborepoAccessTraceResult,\n} from '../build/turborepo-access-trace'\nimport type { Params } from '../server/request/params'\nimport {\n  getFallbackRouteParams,\n  type FallbackRouteParams,\n} from '../server/request/fallback-params'\nimport { needsExperimentalReact } from '../lib/needs-experimental-react'\nimport type { AppRouteRouteModule } from '../server/route-modules/app-route/module.compiled'\nimport { isStaticGenBailoutError } from '../client/components/static-generation-bailout'\nimport type { PagesRenderContext, PagesSharedContext } from '../server/render'\nimport type { AppSharedContext } from '../server/app-render/app-render'\nimport { MultiFileWriter } from '../lib/multi-file-writer'\n\nconst envConfig = require('../shared/lib/runtime-config.external')\n\n;(globalThis as any).__NEXT_DATA__ = {\n  nextExport: true,\n}\n\nclass TimeoutError extends Error {\n  code = 'NEXT_EXPORT_TIMEOUT_ERROR'\n}\n\nclass ExportPageError extends Error {\n  code = 'NEXT_EXPORT_PAGE_ERROR'\n}\n\nasync function exportPageImpl(\n  input: ExportPageInput,\n  fileWriter: MultiFileWriter\n): Promise<ExportRouteResult | undefined> {\n  const {\n    path,\n    pathMap,\n    distDir,\n    pagesDataDir,\n    buildExport = false,\n    serverRuntimeConfig,\n    subFolders = false,\n    optimizeCss,\n    disableOptimizedLoading,\n    debugOutput = false,\n    enableExperimentalReact,\n    ampValidatorPath,\n    trailingSlash,\n    sriEnabled,\n  } = input\n\n  if (enableExperimentalReact) {\n    process.env.__NEXT_EXPERIMENTAL_REACT = 'true'\n  }\n\n  const {\n    page,\n\n    // The parameters that are currently unknown.\n    _fallbackRouteParams = [],\n\n    // Check if this is an `app/` page.\n    _isAppDir: isAppDir = false,\n\n    // Check if this should error when dynamic usage is detected.\n    _isDynamicError: isDynamicError = false,\n\n    // If this page supports partial prerendering, then we need to pass that to\n    // the renderOpts.\n    _isRoutePPREnabled: isRoutePPREnabled,\n\n    // If this is a prospective render, we don't actually want to persist the\n    // result, we just want to use it to error the build if there's a problem.\n    _isProspectiveRender: isProspectiveRender = false,\n\n    // Pull the original query out.\n    query: originalQuery = {},\n  } = pathMap\n\n  const fallbackRouteParams: FallbackRouteParams | null =\n    getFallbackRouteParams(_fallbackRouteParams)\n\n  let query = { ...originalQuery }\n  const pathname = normalizeAppPath(page)\n  const isDynamic = isDynamicRoute(page)\n  const outDir = isAppDir ? join(distDir, 'server/app') : input.outDir\n\n  const filePath = normalizePagePath(path)\n  const ampPath = `${filePath}.amp`\n  let renderAmpPath = ampPath\n\n  let updatedPath = pathMap._ssgPath || path\n  let locale = pathMap._locale || input.renderOpts.locale\n\n  if (input.renderOpts.locale) {\n    const localePathResult = normalizeLocalePath(path, input.renderOpts.locales)\n\n    if (localePathResult.detectedLocale) {\n      updatedPath = localePathResult.pathname\n      locale = localePathResult.detectedLocale\n\n      if (locale === input.renderOpts.defaultLocale) {\n        renderAmpPath = `${normalizePagePath(updatedPath)}.amp`\n      }\n    }\n  }\n\n  // We need to show a warning if they try to provide query values\n  // for an auto-exported page since they won't be available\n  const hasOrigQueryValues = Object.keys(originalQuery).length > 0\n\n  // Check if the page is a specified dynamic route\n  const { pathname: nonLocalizedPath } = normalizeLocalePath(\n    path,\n    input.renderOpts.locales\n  )\n\n  let params: Params | undefined\n\n  if (isDynamic && page !== nonLocalizedPath) {\n    const normalizedPage = isAppDir ? normalizeAppPath(page) : page\n\n    params = getParams(normalizedPage, updatedPath)\n  }\n\n  const { req, res } = createRequestResponseMocks({ url: updatedPath })\n\n  // If this is a status code page, then set the response code.\n  for (const statusCode of [404, 500]) {\n    if (\n      [\n        `/${statusCode}`,\n        `/${statusCode}.html`,\n        `/${statusCode}/index.html`,\n      ].some((p) => p === updatedPath || `/${locale}${p}` === updatedPath)\n    ) {\n      res.statusCode = statusCode\n    }\n  }\n\n  // Ensure that the URL has a trailing slash if it's configured.\n  if (trailingSlash && !req.url?.endsWith('/')) {\n    req.url += '/'\n  }\n\n  if (\n    locale &&\n    buildExport &&\n    input.renderOpts.domainLocales &&\n    input.renderOpts.domainLocales.some(\n      (dl) => dl.defaultLocale === locale || dl.locales?.includes(locale || '')\n    )\n  ) {\n    addRequestMeta(req, 'isLocaleDomain', true)\n  }\n\n  envConfig.setConfig({\n    serverRuntimeConfig,\n    publicRuntimeConfig: input.renderOpts.runtimeConfig,\n  })\n\n  const getHtmlFilename = (p: string) =>\n    subFolders ? `${p}${sep}index.html` : `${p}.html`\n\n  let htmlFilename = getHtmlFilename(filePath)\n\n  // dynamic routes can provide invalid extensions e.g. /blog/[...slug] returns an\n  // extension of `.slug]`\n  const pageExt = isDynamic || isAppDir ? '' : extname(page)\n  const pathExt = isDynamic || isAppDir ? '' : extname(path)\n\n  // force output 404.html for backwards compat\n  if (path === '/404.html') {\n    htmlFilename = path\n  }\n  // Make sure page isn't a folder with a dot in the name e.g. `v1.2`\n  else if (pageExt !== pathExt && pathExt !== '') {\n    const isBuiltinPaths = ['/500', '/404'].some(\n      (p) => p === path || p === path + '.html'\n    )\n    // If the ssg path has .html extension, and it's not builtin paths, use it directly\n    // Otherwise, use that as the filename instead\n    const isHtmlExtPath = !isBuiltinPaths && path.endsWith('.html')\n    htmlFilename = isHtmlExtPath ? getHtmlFilename(path) : path\n  } else if (path === '/') {\n    // If the path is the root, just use index.html\n    htmlFilename = 'index.html'\n  }\n\n  const baseDir = join(outDir, dirname(htmlFilename))\n  let htmlFilepath = join(outDir, htmlFilename)\n\n  await fs.mkdir(baseDir, { recursive: true })\n\n  const components = await loadComponents({\n    distDir,\n    page,\n    isAppPath: isAppDir,\n    isDev: false,\n    sriEnabled,\n  })\n\n  // Handle App Routes.\n  if (isAppDir && isAppRouteRoute(page)) {\n    return exportAppRoute(\n      req,\n      res,\n      params,\n      page,\n      components.routeModule as AppRouteRouteModule,\n      input.renderOpts.incrementalCache,\n      input.renderOpts.cacheLifeProfiles,\n      htmlFilepath,\n      fileWriter,\n      input.renderOpts.experimental,\n      input.buildId\n    )\n  }\n\n  // During the export phase in next build, if it's using PPR we can serve streaming metadata\n  // when it's available. When we're building the PPR rendering result, we don't need to rely\n  // on the user agent. The result can be determined to serve streaming on infrastructure level.\n  const serveStreamingMetadata = !!isRoutePPREnabled\n\n  const renderOpts: WorkerRenderOpts = {\n    ...components,\n    ...input.renderOpts,\n    ampPath: renderAmpPath,\n    params,\n    optimizeCss,\n    disableOptimizedLoading,\n    locale,\n    supportsDynamicResponse: false,\n    serveStreamingMetadata,\n    experimental: {\n      ...input.renderOpts.experimental,\n      isRoutePPREnabled,\n    },\n  }\n\n  if (hasNextSupport) {\n    renderOpts.isRevalidate = true\n  }\n\n  // Handle App Pages\n  if (isAppDir) {\n    const sharedContext: AppSharedContext = {\n      buildId: input.buildId,\n    }\n\n    // If this is a prospective render, don't return any metrics or revalidate\n    // timings as we aren't persisting this render (it was only to error).\n    if (isProspectiveRender) {\n      return prospectiveRenderAppPage(\n        req,\n        res,\n        page,\n        pathname,\n        query,\n        fallbackRouteParams,\n        renderOpts,\n        sharedContext\n      )\n    }\n\n    return exportAppPage(\n      req,\n      res,\n      page,\n      path,\n      pathname,\n      query,\n      fallbackRouteParams,\n      renderOpts,\n      htmlFilepath,\n      debugOutput,\n      isDynamicError,\n      fileWriter,\n      sharedContext\n    )\n  }\n\n  const sharedContext: PagesSharedContext = {\n    buildId: input.buildId,\n    deploymentId: input.renderOpts.deploymentId,\n    customServer: undefined,\n  }\n\n  const renderContext: PagesRenderContext = {\n    isFallback: pathMap._pagesFallback ?? false,\n    isDraftMode: false,\n    developmentNotFoundSourcePage: undefined,\n  }\n\n  return exportPagesPage(\n    req,\n    res,\n    path,\n    page,\n    query,\n    params,\n    htmlFilepath,\n    htmlFilename,\n    ampPath,\n    subFolders,\n    outDir,\n    ampValidatorPath,\n    pagesDataDir,\n    buildExport,\n    isDynamic,\n    sharedContext,\n    renderContext,\n    hasOrigQueryValues,\n    renderOpts,\n    components,\n    fileWriter\n  )\n}\n\nexport async function exportPages(\n  input: ExportPagesInput\n): Promise<ExportPagesResult> {\n  const {\n    exportPathMap,\n    paths,\n    dir,\n    distDir,\n    outDir,\n    cacheHandler,\n    cacheMaxMemorySize,\n    fetchCacheKeyPrefix,\n    pagesDataDir,\n    renderOpts,\n    nextConfig,\n    options,\n  } = input\n\n  // If the fetch cache was enabled, we need to create an incremental\n  // cache instance for this page.\n  const incrementalCache = await createIncrementalCache({\n    cacheHandler,\n    cacheMaxMemorySize,\n    fetchCacheKeyPrefix,\n    distDir,\n    dir,\n    // skip writing to disk in minimal mode for now, pending some\n    // changes to better support it\n    flushToDisk: !hasNextSupport,\n    cacheHandlers: nextConfig.experimental.cacheHandlers,\n  })\n\n  renderOpts.incrementalCache = incrementalCache\n\n  const maxConcurrency =\n    nextConfig.experimental.staticGenerationMaxConcurrency ?? 8\n  const results: ExportPagesResult = []\n\n  const exportPageWithRetry = async (path: string, maxAttempts: number) => {\n    const pathMap = exportPathMap[path]\n    const { page } = exportPathMap[path]\n    const pageKey = page !== path ? `${page}: ${path}` : path\n    let attempt = 0\n    let result\n\n    while (attempt < maxAttempts) {\n      try {\n        result = await Promise.race<ExportPageResult | undefined>([\n          exportPage({\n            path,\n            pathMap,\n            distDir,\n            outDir,\n            pagesDataDir,\n            renderOpts,\n            ampValidatorPath:\n              nextConfig.experimental.amp?.validator || undefined,\n            trailingSlash: nextConfig.trailingSlash,\n            serverRuntimeConfig: nextConfig.serverRuntimeConfig,\n            subFolders: nextConfig.trailingSlash && !options.buildExport,\n            buildExport: options.buildExport,\n            optimizeCss: nextConfig.experimental.optimizeCss,\n            disableOptimizedLoading:\n              nextConfig.experimental.disableOptimizedLoading,\n            parentSpanId: input.parentSpanId,\n            httpAgentOptions: nextConfig.httpAgentOptions,\n            debugOutput: options.debugOutput,\n            enableExperimentalReact: needsExperimentalReact(nextConfig),\n            sriEnabled: Boolean(nextConfig.experimental.sri?.algorithm),\n            buildId: input.buildId,\n          }),\n          // If exporting the page takes longer than the timeout, reject the promise.\n          new Promise((_, reject) => {\n            setTimeout(() => {\n              reject(new TimeoutError())\n            }, nextConfig.staticPageGenerationTimeout * 1000)\n          }),\n        ])\n\n        // If there was an error in the export, throw it immediately. In the catch block, we might retry the export,\n        // or immediately fail the build, depending on user configuration. We might also continue on and attempt other pages.\n        if (result && 'error' in result) {\n          throw new ExportPageError()\n        }\n\n        // If the export succeeds, break out of the retry loop\n        break\n      } catch (err) {\n        // The only error that should be caught here is an ExportError, as `exportPage` doesn't throw and instead returns an object with an `error` property.\n        // This is an overly cautious check to ensure that we don't accidentally catch an unexpected error.\n        if (!(err instanceof ExportPageError || err instanceof TimeoutError)) {\n          throw err\n        }\n\n        if (err instanceof TimeoutError) {\n          // If the export times out, we will restart the worker up to 3 times.\n          maxAttempts = 3\n        }\n\n        // We've reached the maximum number of attempts\n        if (attempt >= maxAttempts - 1) {\n          // Log a message if we've reached the maximum number of attempts.\n          // We only care to do this if maxAttempts was configured.\n          if (maxAttempts > 1) {\n            console.info(\n              `Failed to build ${pageKey} after ${maxAttempts} attempts.`\n            )\n          }\n          // If prerenderEarlyExit is enabled, we'll exit the build immediately.\n          if (nextConfig.experimental.prerenderEarlyExit) {\n            console.error(\n              `Export encountered an error on ${pageKey}, exiting the build.`\n            )\n            process.exit(1)\n          } else {\n            // Otherwise, this is a no-op. The build will continue, and a summary of failed pages will be displayed at the end.\n          }\n        } else {\n          // Otherwise, we have more attempts to make. Wait before retrying\n          if (err instanceof TimeoutError) {\n            console.info(\n              `Failed to build ${pageKey} (attempt ${attempt + 1} of ${maxAttempts}) because it took more than ${nextConfig.staticPageGenerationTimeout} seconds. Retrying again shortly.`\n            )\n          } else {\n            console.info(\n              `Failed to build ${pageKey} (attempt ${attempt + 1} of ${maxAttempts}). Retrying again shortly.`\n            )\n          }\n\n          // Exponential backoff with random jitter to avoid thundering herd on retries\n          const baseDelay = 500 // 500ms\n          const maxDelay = 2000 // 2 seconds\n          const delay = Math.min(baseDelay * Math.pow(2, attempt), maxDelay)\n          const jitter = Math.random() * 0.3 * delay // Add up to 30% random jitter\n          await new Promise((r) => setTimeout(r, delay + jitter))\n        }\n      }\n\n      attempt++\n    }\n\n    return { result, path, pageKey }\n  }\n\n  for (let i = 0; i < paths.length; i += maxConcurrency) {\n    const subset = paths.slice(i, i + maxConcurrency)\n\n    const subsetResults = await Promise.all(\n      subset.map((path) =>\n        exportPageWithRetry(\n          path,\n          nextConfig.experimental.staticGenerationRetryCount ?? 1\n        )\n      )\n    )\n\n    results.push(...subsetResults)\n  }\n\n  return results\n}\n\nasync function exportPage(\n  input: ExportPageInput\n): Promise<ExportPageResult | undefined> {\n  trace('export-page', input.parentSpanId).setAttribute('path', input.path)\n\n  // Configure the http agent.\n  setHttpClientAndAgentOptions({\n    httpAgentOptions: input.httpAgentOptions,\n  })\n\n  const fileWriter = new MultiFileWriter({\n    writeFile: (filePath, data) => fs.writeFile(filePath, data),\n    mkdir: (dir) => fs.mkdir(dir, { recursive: true }),\n  })\n\n  const exportPageSpan = trace('export-page-worker', input.parentSpanId)\n\n  const start = Date.now()\n\n  const turborepoAccessTraceResult = new TurborepoAccessTraceResult()\n\n  // Export the page.\n  let result: ExportRouteResult | undefined\n  try {\n    result = await exportPageSpan.traceAsyncFn(() =>\n      turborepoTraceAccess(\n        () => exportPageImpl(input, fileWriter),\n        turborepoAccessTraceResult\n      )\n    )\n\n    // Wait for all the files to flush to disk.\n    await fileWriter.wait()\n\n    // If there was no result, then we can exit early.\n    if (!result) return\n\n    // If there was an error, then we can exit early.\n    if ('error' in result) {\n      return { error: result.error, duration: Date.now() - start }\n    }\n  } catch (err) {\n    console.error(\n      `Error occurred prerendering page \"${input.path}\". Read more: https://nextjs.org/docs/messages/prerender-error`\n    )\n\n    // bailoutToCSRError errors should not leak to the user as they are not actionable; they're\n    // a framework signal\n    if (!isBailoutToCSRError(err)) {\n      // A static generation bailout error is a framework signal to fail static generation but\n      // and will encode a reason in the error message. If there is a message, we'll print it.\n      // Otherwise there's nothing to show as we don't want to leak an error internal error stack to the user.\n      if (isStaticGenBailoutError(err)) {\n        if (err.message) {\n          console.error(`Error: ${err.message}`)\n        }\n      } else if (isError(err) && err.stack) {\n        console.error(err.stack)\n      } else {\n        console.error(err)\n      }\n    }\n\n    return { error: true, duration: Date.now() - start }\n  }\n\n  // Notify the parent process that we processed a page (used by the progress activity indicator)\n  process.send?.([3, { type: 'activity' }])\n\n  // Otherwise we can return the result.\n  return {\n    duration: Date.now() - start,\n    ampValidations: result.ampValidations,\n    cacheControl: result.cacheControl,\n    metadata: result.metadata,\n    ssgNotFound: result.ssgNotFound,\n    hasEmptyPrelude: result.hasEmptyPrelude,\n    hasPostponed: result.hasPostponed,\n    turborepoAccessTraceResult: turborepoAccessTraceResult.serialize(),\n    fetchMetrics: result.fetchMetrics,\n  }\n}\n\nprocess.on('unhandledRejection', (err: unknown) => {\n  // if it's a postpone error, it'll be handled later\n  // when the postponed promise is actually awaited.\n  if (isPostpone(err)) {\n    return\n  }\n\n  // we don't want to log these errors\n  if (isDynamicUsageError(err)) {\n    return\n  }\n\n  console.error(err)\n})\n\nprocess.on('rejectionHandled', () => {\n  // It is ok to await a Promise late in Next.js as it allows for better\n  // prefetching patterns to avoid waterfalls. We ignore logging these.\n  // We should've already errored in anyway unhandledRejection.\n})\n\nconst FATAL_UNHANDLED_NEXT_API_EXIT_CODE = 78\n\nprocess.on('uncaughtException', (err) => {\n  if (isDynamicUsageError(err)) {\n    console.error(\n      'A Next.js API that uses exceptions to signal framework behavior was uncaught. This suggests improper usage of a Next.js API. The original error is printed below and the build will now exit.'\n    )\n    console.error(err)\n    process.exit(FATAL_UNHANDLED_NEXT_API_EXIT_CODE)\n  } else {\n    console.error(err)\n  }\n})\n"], "names": ["process", "env", "NEXT_IS_EXPORT_WORKER", "extname", "join", "dirname", "sep", "fs", "loadComponents", "isDynamicRoute", "normalizePagePath", "normalizeLocalePath", "trace", "setHttpClientAndAgentOptions", "isError", "addRequestMeta", "normalizeAppPath", "createRequestResponseMocks", "isAppRouteRoute", "hasNextSupport", "exportAppRoute", "exportAppPage", "prospectiveRenderAppPage", "exportPagesPage", "getParams", "createIncrementalCache", "isPostpone", "isDynamicUsageError", "isBailoutToCSRError", "turborepoTraceAccess", "TurborepoAccessTraceResult", "getFallbackRouteParams", "needsExperimentalReact", "isStaticGenBailoutError", "MultiFileWriter", "envConfig", "require", "globalThis", "__NEXT_DATA__", "nextExport", "TimeoutError", "Error", "code", "ExportPageError", "exportPageImpl", "input", "fileWriter", "req", "path", "pathMap", "distDir", "pagesDataDir", "buildExport", "serverRuntimeConfig", "subFolders", "optimizeCss", "disableOptimizedLoading", "debugOutput", "enableExperimentalReact", "ampValidator<PERSON>ath", "trailingSlash", "sriEnabled", "__NEXT_EXPERIMENTAL_REACT", "page", "_fallbackRouteParams", "_isAppDir", "isAppDir", "_isDynamicError", "isDynamicError", "_isRoutePPREnabled", "isRoutePPREnabled", "_isProspectiveRender", "isProspectiveRender", "query", "originalQuery", "fallbackRouteParams", "pathname", "isDynamic", "outDir", "filePath", "ampPath", "renderAmpPath", "updatedPath", "_ssgPath", "locale", "_locale", "renderOpts", "localePathResult", "locales", "detectedLocale", "defaultLocale", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Object", "keys", "length", "nonLocalizedPath", "params", "normalizedPage", "res", "url", "statusCode", "some", "p", "endsWith", "domainLocales", "dl", "includes", "setConfig", "publicRuntimeConfig", "runtimeConfig", "getHtmlFilename", "htmlFilename", "pageExt", "pathExt", "isBuiltinPaths", "isHtmlExtPath", "baseDir", "htmlFilepath", "mkdir", "recursive", "components", "isAppPath", "isDev", "routeModule", "incrementalCache", "cacheLifeProfiles", "experimental", "buildId", "serveStreamingMetadata", "supportsDynamicResponse", "isRevalidate", "sharedContext", "deploymentId", "customServer", "undefined", "renderContext", "<PERSON><PERSON><PERSON><PERSON>", "_pagesFallback", "isDraftMode", "developmentNotFoundSourcePage", "exportPages", "exportPathMap", "paths", "dir", "cache<PERSON><PERSON><PERSON>", "cacheMaxMemorySize", "fetchCacheKeyPrefix", "nextConfig", "options", "flushToDisk", "cacheHandlers", "maxConcurrency", "staticGenerationMaxConcurrency", "results", "exportPageWithRetry", "maxAttempts", "page<PERSON><PERSON>", "attempt", "result", "Promise", "race", "exportPage", "amp", "validator", "parentSpanId", "httpAgentOptions", "Boolean", "sri", "algorithm", "_", "reject", "setTimeout", "staticPageGenerationTimeout", "err", "console", "info", "prerenderEarlyExit", "error", "exit", "baseDelay", "max<PERSON><PERSON><PERSON>", "delay", "Math", "min", "pow", "jitter", "random", "r", "i", "subset", "slice", "subsetResults", "all", "map", "staticGenerationRetryCount", "push", "setAttribute", "writeFile", "data", "exportPageSpan", "start", "Date", "now", "turborepoAccessTraceResult", "traceAsyncFn", "wait", "duration", "message", "stack", "send", "type", "ampValidations", "cacheControl", "metadata", "ssgNotFound", "hasEmptyPrelude", "hasPostponed", "serialize", "fetchMetrics", "on", "FATAL_UNHANDLED_NEXT_API_EXIT_CODE"], "mappings": "AASA,OAAO,6BAA4B;AAEnCA,QAAQC,GAAG,CAACC,qBAAqB,GAAG;AAEpC,SAASC,OAAO,EAAEC,IAAI,EAAEC,OAAO,EAAEC,GAAG,QAAQ,OAAM;AAClD,OAAOC,QAAQ,cAAa;AAC5B,SAASC,cAAc,QAAQ,4BAA2B;AAC1D,SAASC,cAAc,QAAQ,wCAAuC;AACtE,SAASC,iBAAiB,QAAQ,8CAA6C;AAC/E,SAASC,mBAAmB,QAAQ,2CAA0C;AAC9E,SAASC,KAAK,QAAQ,WAAU;AAChC,SAASC,4BAA4B,QAAQ,iCAAgC;AAC7E,OAAOC,aAAa,kBAAiB;AACrC,SAASC,cAAc,QAAQ,yBAAwB;AACvD,SAASC,gBAAgB,QAAQ,uCAAsC;AAEvE,SAASC,0BAA0B,QAAQ,6BAA4B;AACvE,SAASC,eAAe,QAAQ,4BAA2B;AAC3D,SAASC,cAAc,QAAQ,oBAAmB;AAClD,SAASC,cAAc,QAAQ,qBAAoB;AACnD,SAASC,aAAa,EAAEC,wBAAwB,QAAQ,oBAAmB;AAC3E,SAASC,eAAe,QAAQ,iBAAgB;AAChD,SAASC,SAAS,QAAQ,uBAAsB;AAChD,SAASC,sBAAsB,QAAQ,qCAAoC;AAC3E,SAASC,UAAU,QAAQ,yCAAwC;AACnE,SAASC,mBAAmB,QAAQ,mCAAkC;AACtE,SAASC,mBAAmB,QAAQ,4CAA2C;AAC/E,SACEC,oBAAoB,EACpBC,0BAA0B,QACrB,kCAAiC;AAExC,SACEC,sBAAsB,QAEjB,oCAAmC;AAC1C,SAASC,sBAAsB,QAAQ,kCAAiC;AAExE,SAASC,uBAAuB,QAAQ,iDAAgD;AAGxF,SAASC,eAAe,QAAQ,2BAA0B;AAE1D,MAAMC,YAAYC,QAAQ;AAExBC,WAAmBC,aAAa,GAAG;IACnCC,YAAY;AACd;AAEA,MAAMC,qBAAqBC;;QAA3B,qBACEC,OAAO;;AACT;AAEA,MAAMC,wBAAwBF;;QAA9B,qBACEC,OAAO;;AACT;AAEA,eAAeE,eACbC,KAAsB,EACtBC,UAA2B;QA6GLC;IA3GtB,MAAM,EACJC,IAAI,EACJC,OAAO,EACPC,OAAO,EACPC,YAAY,EACZC,cAAc,KAAK,EACnBC,mBAAmB,EACnBC,aAAa,KAAK,EAClBC,WAAW,EACXC,uBAAuB,EACvBC,cAAc,KAAK,EACnBC,uBAAuB,EACvBC,gBAAgB,EAChBC,aAAa,EACbC,UAAU,EACX,GAAGhB;IAEJ,IAAIa,yBAAyB;QAC3B1D,QAAQC,GAAG,CAAC6D,yBAAyB,GAAG;IAC1C;IAEA,MAAM,EACJC,IAAI,EAEJ,6CAA6C;IAC7CC,uBAAuB,EAAE,EAEzB,mCAAmC;IACnCC,WAAWC,WAAW,KAAK,EAE3B,6DAA6D;IAC7DC,iBAAiBC,iBAAiB,KAAK,EAEvC,2EAA2E;IAC3E,kBAAkB;IAClBC,oBAAoBC,iBAAiB,EAErC,yEAAyE;IACzE,0EAA0E;IAC1EC,sBAAsBC,sBAAsB,KAAK,EAEjD,+BAA+B;IAC/BC,OAAOC,gBAAgB,CAAC,CAAC,EAC1B,GAAGzB;IAEJ,MAAM0B,sBACJ5C,uBAAuBiC;IAEzB,IAAIS,QAAQ;QAAE,GAAGC,aAAa;IAAC;IAC/B,MAAME,WAAW5D,iBAAiB+C;IAClC,MAAMc,YAAYpE,eAAesD;IACjC,MAAMe,SAASZ,WAAW9D,KAAK8C,SAAS,gBAAgBL,MAAMiC,MAAM;IAEpE,MAAMC,WAAWrE,kBAAkBsC;IACnC,MAAMgC,UAAU,GAAGD,SAAS,IAAI,CAAC;IACjC,IAAIE,gBAAgBD;IAEpB,IAAIE,cAAcjC,QAAQkC,QAAQ,IAAInC;IACtC,IAAIoC,SAASnC,QAAQoC,OAAO,IAAIxC,MAAMyC,UAAU,CAACF,MAAM;IAEvD,IAAIvC,MAAMyC,UAAU,CAACF,MAAM,EAAE;QAC3B,MAAMG,mBAAmB5E,oBAAoBqC,MAAMH,MAAMyC,UAAU,CAACE,OAAO;QAE3E,IAAID,iBAAiBE,cAAc,EAAE;YACnCP,cAAcK,iBAAiBX,QAAQ;YACvCQ,SAASG,iBAAiBE,cAAc;YAExC,IAAIL,WAAWvC,MAAMyC,UAAU,CAACI,aAAa,EAAE;gBAC7CT,gBAAgB,GAAGvE,kBAAkBwE,aAAa,IAAI,CAAC;YACzD;QACF;IACF;IAEA,gEAAgE;IAChE,0DAA0D;IAC1D,MAAMS,qBAAqBC,OAAOC,IAAI,CAACnB,eAAeoB,MAAM,GAAG;IAE/D,iDAAiD;IACjD,MAAM,EAAElB,UAAUmB,gBAAgB,EAAE,GAAGpF,oBACrCqC,MACAH,MAAMyC,UAAU,CAACE,OAAO;IAG1B,IAAIQ;IAEJ,IAAInB,aAAad,SAASgC,kBAAkB;QAC1C,MAAME,iBAAiB/B,WAAWlD,iBAAiB+C,QAAQA;QAE3DiC,SAASxE,UAAUyE,gBAAgBf;IACrC;IAEA,MAAM,EAAEnC,GAAG,EAAEmD,GAAG,EAAE,GAAGjF,2BAA2B;QAAEkF,KAAKjB;IAAY;IAEnE,6DAA6D;IAC7D,KAAK,MAAMkB,cAAc;QAAC;QAAK;KAAI,CAAE;QACnC,IACE;YACE,CAAC,CAAC,EAAEA,YAAY;YAChB,CAAC,CAAC,EAAEA,WAAW,KAAK,CAAC;YACrB,CAAC,CAAC,EAAEA,WAAW,WAAW,CAAC;SAC5B,CAACC,IAAI,CAAC,CAACC,IAAMA,MAAMpB,eAAe,CAAC,CAAC,EAAEE,SAASkB,GAAG,KAAKpB,cACxD;YACAgB,IAAIE,UAAU,GAAGA;QACnB;IACF;IAEA,+DAA+D;IAC/D,IAAIxC,iBAAiB,GAACb,WAAAA,IAAIoD,GAAG,qBAAPpD,SAASwD,QAAQ,CAAC,OAAM;QAC5CxD,IAAIoD,GAAG,IAAI;IACb;IAEA,IACEf,UACAhC,eACAP,MAAMyC,UAAU,CAACkB,aAAa,IAC9B3D,MAAMyC,UAAU,CAACkB,aAAa,CAACH,IAAI,CACjC,CAACI;YAAsCA;eAA/BA,GAAGf,aAAa,KAAKN,YAAUqB,cAAAA,GAAGjB,OAAO,qBAAViB,YAAYC,QAAQ,CAACtB,UAAU;QAExE;QACArE,eAAegC,KAAK,kBAAkB;IACxC;IAEAZ,UAAUwE,SAAS,CAAC;QAClBtD;QACAuD,qBAAqB/D,MAAMyC,UAAU,CAACuB,aAAa;IACrD;IAEA,MAAMC,kBAAkB,CAACR,IACvBhD,aAAa,GAAGgD,IAAIhG,IAAI,UAAU,CAAC,GAAG,GAAGgG,EAAE,KAAK,CAAC;IAEnD,IAAIS,eAAeD,gBAAgB/B;IAEnC,gFAAgF;IAChF,wBAAwB;IACxB,MAAMiC,UAAUnC,aAAaX,WAAW,KAAK/D,QAAQ4D;IACrD,MAAMkD,UAAUpC,aAAaX,WAAW,KAAK/D,QAAQ6C;IAErD,6CAA6C;IAC7C,IAAIA,SAAS,aAAa;QACxB+D,eAAe/D;IACjB,OAEK,IAAIgE,YAAYC,WAAWA,YAAY,IAAI;QAC9C,MAAMC,iBAAiB;YAAC;YAAQ;SAAO,CAACb,IAAI,CAC1C,CAACC,IAAMA,MAAMtD,QAAQsD,MAAMtD,OAAO;QAEpC,mFAAmF;QACnF,8CAA8C;QAC9C,MAAMmE,gBAAgB,CAACD,kBAAkBlE,KAAKuD,QAAQ,CAAC;QACvDQ,eAAeI,gBAAgBL,gBAAgB9D,QAAQA;IACzD,OAAO,IAAIA,SAAS,KAAK;QACvB,+CAA+C;QAC/C+D,eAAe;IACjB;IAEA,MAAMK,UAAUhH,KAAK0E,QAAQzE,QAAQ0G;IACrC,IAAIM,eAAejH,KAAK0E,QAAQiC;IAEhC,MAAMxG,GAAG+G,KAAK,CAACF,SAAS;QAAEG,WAAW;IAAK;IAE1C,MAAMC,aAAa,MAAMhH,eAAe;QACtC0C;QACAa;QACA0D,WAAWvD;QACXwD,OAAO;QACP7D;IACF;IAEA,qBAAqB;IACrB,IAAIK,YAAYhD,gBAAgB6C,OAAO;QACrC,OAAO3C,eACL2B,KACAmD,KACAF,QACAjC,MACAyD,WAAWG,WAAW,EACtB9E,MAAMyC,UAAU,CAACsC,gBAAgB,EACjC/E,MAAMyC,UAAU,CAACuC,iBAAiB,EAClCR,cACAvE,YACAD,MAAMyC,UAAU,CAACwC,YAAY,EAC7BjF,MAAMkF,OAAO;IAEjB;IAEA,2FAA2F;IAC3F,2FAA2F;IAC3F,8FAA8F;IAC9F,MAAMC,yBAAyB,CAAC,CAAC1D;IAEjC,MAAMgB,aAA+B;QACnC,GAAGkC,UAAU;QACb,GAAG3E,MAAMyC,UAAU;QACnBN,SAASC;QACTe;QACAzC;QACAC;QACA4B;QACA6C,yBAAyB;QACzBD;QACAF,cAAc;YACZ,GAAGjF,MAAMyC,UAAU,CAACwC,YAAY;YAChCxD;QACF;IACF;IAEA,IAAInD,gBAAgB;QAClBmE,WAAW4C,YAAY,GAAG;IAC5B;IAEA,mBAAmB;IACnB,IAAIhE,UAAU;QACZ,MAAMiE,gBAAkC;YACtCJ,SAASlF,MAAMkF,OAAO;QACxB;QAEA,0EAA0E;QAC1E,sEAAsE;QACtE,IAAIvD,qBAAqB;YACvB,OAAOlD,yBACLyB,KACAmD,KACAnC,MACAa,UACAH,OACAE,qBACAW,YACA6C;QAEJ;QAEA,OAAO9G,cACL0B,KACAmD,KACAnC,MACAf,MACA4B,UACAH,OACAE,qBACAW,YACA+B,cACA5D,aACAW,gBACAtB,YACAqF;IAEJ;IAEA,MAAMA,gBAAoC;QACxCJ,SAASlF,MAAMkF,OAAO;QACtBK,cAAcvF,MAAMyC,UAAU,CAAC8C,YAAY;QAC3CC,cAAcC;IAChB;IAEA,MAAMC,gBAAoC;QACxCC,YAAYvF,QAAQwF,cAAc,IAAI;QACtCC,aAAa;QACbC,+BAA+BL;IACjC;IAEA,OAAO/G,gBACLwB,KACAmD,KACAlD,MACAe,MACAU,OACAuB,QACAqB,cACAN,cACA/B,SACA1B,YACAwB,QACAnB,kBACAR,cACAC,aACAyB,WACAsD,eACAI,eACA5C,oBACAL,YACAkC,YACA1E;AAEJ;AAEA,OAAO,eAAe8F,YACpB/F,KAAuB;IAEvB,MAAM,EACJgG,aAAa,EACbC,KAAK,EACLC,GAAG,EACH7F,OAAO,EACP4B,MAAM,EACNkE,YAAY,EACZC,kBAAkB,EAClBC,mBAAmB,EACnB/F,YAAY,EACZmC,UAAU,EACV6D,UAAU,EACVC,OAAO,EACR,GAAGvG;IAEJ,mEAAmE;IACnE,gCAAgC;IAChC,MAAM+E,mBAAmB,MAAMnG,uBAAuB;QACpDuH;QACAC;QACAC;QACAhG;QACA6F;QACA,6DAA6D;QAC7D,+BAA+B;QAC/BM,aAAa,CAAClI;QACdmI,eAAeH,WAAWrB,YAAY,CAACwB,aAAa;IACtD;IAEAhE,WAAWsC,gBAAgB,GAAGA;IAE9B,MAAM2B,iBACJJ,WAAWrB,YAAY,CAAC0B,8BAA8B,IAAI;IAC5D,MAAMC,UAA6B,EAAE;IAErC,MAAMC,sBAAsB,OAAO1G,MAAc2G;QAC/C,MAAM1G,UAAU4F,aAAa,CAAC7F,KAAK;QACnC,MAAM,EAAEe,IAAI,EAAE,GAAG8E,aAAa,CAAC7F,KAAK;QACpC,MAAM4G,UAAU7F,SAASf,OAAO,GAAGe,KAAK,EAAE,EAAEf,MAAM,GAAGA;QACrD,IAAI6G,UAAU;QACd,IAAIC;QAEJ,MAAOD,UAAUF,YAAa;YAC5B,IAAI;oBAUIR,8BAYkBA;gBArBxBW,SAAS,MAAMC,QAAQC,IAAI,CAA+B;oBACxDC,WAAW;wBACTjH;wBACAC;wBACAC;wBACA4B;wBACA3B;wBACAmC;wBACA3B,kBACEwF,EAAAA,+BAAAA,WAAWrB,YAAY,CAACoC,GAAG,qBAA3Bf,6BAA6BgB,SAAS,KAAI7B;wBAC5C1E,eAAeuF,WAAWvF,aAAa;wBACvCP,qBAAqB8F,WAAW9F,mBAAmB;wBACnDC,YAAY6F,WAAWvF,aAAa,IAAI,CAACwF,QAAQhG,WAAW;wBAC5DA,aAAagG,QAAQhG,WAAW;wBAChCG,aAAa4F,WAAWrB,YAAY,CAACvE,WAAW;wBAChDC,yBACE2F,WAAWrB,YAAY,CAACtE,uBAAuB;wBACjD4G,cAAcvH,MAAMuH,YAAY;wBAChCC,kBAAkBlB,WAAWkB,gBAAgB;wBAC7C5G,aAAa2F,QAAQ3F,WAAW;wBAChCC,yBAAyB1B,uBAAuBmH;wBAChDtF,YAAYyG,SAAQnB,+BAAAA,WAAWrB,YAAY,CAACyC,GAAG,qBAA3BpB,6BAA6BqB,SAAS;wBAC1DzC,SAASlF,MAAMkF,OAAO;oBACxB;oBACA,2EAA2E;oBAC3E,IAAIgC,QAAQ,CAACU,GAAGC;wBACdC,WAAW;4BACTD,OAAO,IAAIlI;wBACb,GAAG2G,WAAWyB,2BAA2B,GAAG;oBAC9C;iBACD;gBAED,4GAA4G;gBAC5G,qHAAqH;gBACrH,IAAId,UAAU,WAAWA,QAAQ;oBAC/B,MAAM,IAAInH;gBACZ;gBAGA;YACF,EAAE,OAAOkI,KAAK;gBACZ,qJAAqJ;gBACrJ,mGAAmG;gBACnG,IAAI,CAAEA,CAAAA,eAAelI,mBAAmBkI,eAAerI,YAAW,GAAI;oBACpE,MAAMqI;gBACR;gBAEA,IAAIA,eAAerI,cAAc;oBAC/B,qEAAqE;oBACrEmH,cAAc;gBAChB;gBAEA,+CAA+C;gBAC/C,IAAIE,WAAWF,cAAc,GAAG;oBAC9B,iEAAiE;oBACjE,yDAAyD;oBACzD,IAAIA,cAAc,GAAG;wBACnBmB,QAAQC,IAAI,CACV,CAAC,gBAAgB,EAAEnB,QAAQ,OAAO,EAAED,YAAY,UAAU,CAAC;oBAE/D;oBACA,sEAAsE;oBACtE,IAAIR,WAAWrB,YAAY,CAACkD,kBAAkB,EAAE;wBAC9CF,QAAQG,KAAK,CACX,CAAC,+BAA+B,EAAErB,QAAQ,oBAAoB,CAAC;wBAEjE5J,QAAQkL,IAAI,CAAC;oBACf,OAAO;oBACL,mHAAmH;oBACrH;gBACF,OAAO;oBACL,iEAAiE;oBACjE,IAAIL,eAAerI,cAAc;wBAC/BsI,QAAQC,IAAI,CACV,CAAC,gBAAgB,EAAEnB,QAAQ,UAAU,EAAEC,UAAU,EAAE,IAAI,EAAEF,YAAY,4BAA4B,EAAER,WAAWyB,2BAA2B,CAAC,iCAAiC,CAAC;oBAEhL,OAAO;wBACLE,QAAQC,IAAI,CACV,CAAC,gBAAgB,EAAEnB,QAAQ,UAAU,EAAEC,UAAU,EAAE,IAAI,EAAEF,YAAY,0BAA0B,CAAC;oBAEpG;oBAEA,6EAA6E;oBAC7E,MAAMwB,YAAY,IAAI,QAAQ;;oBAC9B,MAAMC,WAAW,KAAK,YAAY;;oBAClC,MAAMC,QAAQC,KAAKC,GAAG,CAACJ,YAAYG,KAAKE,GAAG,CAAC,GAAG3B,UAAUuB;oBACzD,MAAMK,SAASH,KAAKI,MAAM,KAAK,MAAML,MAAM,8BAA8B;;oBACzE,MAAM,IAAItB,QAAQ,CAAC4B,IAAMhB,WAAWgB,GAAGN,QAAQI;gBACjD;YACF;YAEA5B;QACF;QAEA,OAAO;YAAEC;YAAQ9G;YAAM4G;QAAQ;IACjC;IAEA,IAAK,IAAIgC,IAAI,GAAGA,IAAI9C,MAAMhD,MAAM,EAAE8F,KAAKrC,eAAgB;QACrD,MAAMsC,SAAS/C,MAAMgD,KAAK,CAACF,GAAGA,IAAIrC;QAElC,MAAMwC,gBAAgB,MAAMhC,QAAQiC,GAAG,CACrCH,OAAOI,GAAG,CAAC,CAACjJ,OACV0G,oBACE1G,MACAmG,WAAWrB,YAAY,CAACoE,0BAA0B,IAAI;QAK5DzC,QAAQ0C,IAAI,IAAIJ;IAClB;IAEA,OAAOtC;AACT;AAEA,eAAeQ,WACbpH,KAAsB;IAEtBjC,MAAM,eAAeiC,MAAMuH,YAAY,EAAEgC,YAAY,CAAC,QAAQvJ,MAAMG,IAAI;IAExE,4BAA4B;IAC5BnC,6BAA6B;QAC3BwJ,kBAAkBxH,MAAMwH,gBAAgB;IAC1C;IAEA,MAAMvH,aAAa,IAAIZ,gBAAgB;QACrCmK,WAAW,CAACtH,UAAUuH,OAAS/L,GAAG8L,SAAS,CAACtH,UAAUuH;QACtDhF,OAAO,CAACyB,MAAQxI,GAAG+G,KAAK,CAACyB,KAAK;gBAAExB,WAAW;YAAK;IAClD;IAEA,MAAMgF,iBAAiB3L,MAAM,sBAAsBiC,MAAMuH,YAAY;IAErE,MAAMoC,QAAQC,KAAKC,GAAG;IAEtB,MAAMC,6BAA6B,IAAI7K;IAEvC,mBAAmB;IACnB,IAAIgI;IACJ,IAAI;QACFA,SAAS,MAAMyC,eAAeK,YAAY,CAAC,IACzC/K,qBACE,IAAMe,eAAeC,OAAOC,aAC5B6J;QAIJ,2CAA2C;QAC3C,MAAM7J,WAAW+J,IAAI;QAErB,kDAAkD;QAClD,IAAI,CAAC/C,QAAQ;QAEb,iDAAiD;QACjD,IAAI,WAAWA,QAAQ;YACrB,OAAO;gBAAEmB,OAAOnB,OAAOmB,KAAK;gBAAE6B,UAAUL,KAAKC,GAAG,KAAKF;YAAM;QAC7D;IACF,EAAE,OAAO3B,KAAK;QACZC,QAAQG,KAAK,CACX,CAAC,kCAAkC,EAAEpI,MAAMG,IAAI,CAAC,8DAA8D,CAAC;QAGjH,2FAA2F;QAC3F,qBAAqB;QACrB,IAAI,CAACpB,oBAAoBiJ,MAAM;YAC7B,wFAAwF;YACxF,wFAAwF;YACxF,wGAAwG;YACxG,IAAI5I,wBAAwB4I,MAAM;gBAChC,IAAIA,IAAIkC,OAAO,EAAE;oBACfjC,QAAQG,KAAK,CAAC,CAAC,OAAO,EAAEJ,IAAIkC,OAAO,EAAE;gBACvC;YACF,OAAO,IAAIjM,QAAQ+J,QAAQA,IAAImC,KAAK,EAAE;gBACpClC,QAAQG,KAAK,CAACJ,IAAImC,KAAK;YACzB,OAAO;gBACLlC,QAAQG,KAAK,CAACJ;YAChB;QACF;QAEA,OAAO;YAAEI,OAAO;YAAM6B,UAAUL,KAAKC,GAAG,KAAKF;QAAM;IACrD;IAEA,+FAA+F;IAC/FxM,QAAQiN,IAAI,oBAAZjN,QAAQiN,IAAI,MAAZjN,SAAe;QAAC;QAAG;YAAEkN,MAAM;QAAW;KAAE;IAExC,sCAAsC;IACtC,OAAO;QACLJ,UAAUL,KAAKC,GAAG,KAAKF;QACvBW,gBAAgBrD,OAAOqD,cAAc;QACrCC,cAActD,OAAOsD,YAAY;QACjCC,UAAUvD,OAAOuD,QAAQ;QACzBC,aAAaxD,OAAOwD,WAAW;QAC/BC,iBAAiBzD,OAAOyD,eAAe;QACvCC,cAAc1D,OAAO0D,YAAY;QACjCb,4BAA4BA,2BAA2Bc,SAAS;QAChEC,cAAc5D,OAAO4D,YAAY;IACnC;AACF;AAEA1N,QAAQ2N,EAAE,CAAC,sBAAsB,CAAC9C;IAChC,mDAAmD;IACnD,kDAAkD;IAClD,IAAInJ,WAAWmJ,MAAM;QACnB;IACF;IAEA,oCAAoC;IACpC,IAAIlJ,oBAAoBkJ,MAAM;QAC5B;IACF;IAEAC,QAAQG,KAAK,CAACJ;AAChB;AAEA7K,QAAQ2N,EAAE,CAAC,oBAAoB;AAC7B,sEAAsE;AACtE,qEAAqE;AACrE,6DAA6D;AAC/D;AAEA,MAAMC,qCAAqC;AAE3C5N,QAAQ2N,EAAE,CAAC,qBAAqB,CAAC9C;IAC/B,IAAIlJ,oBAAoBkJ,MAAM;QAC5BC,QAAQG,KAAK,CACX;QAEFH,QAAQG,KAAK,CAACJ;QACd7K,QAAQkL,IAAI,CAAC0C;IACf,OAAO;QACL9C,QAAQG,KAAK,CAACJ;IAChB;AACF"}