{"version": 3, "sources": ["../../../src/server/dev/turbopack-utils.ts"], "sourcesContent": ["import type {\n  ServerFields,\n  SetupOpts,\n} from '../lib/router-utils/setup-dev-bundler'\nimport type {\n  Issue,\n  TurbopackResult,\n  Endpoint,\n  RawEntrypoints,\n  Update as TurbopackUpdate,\n  WrittenEndpoint,\n} from '../../build/swc/types'\nimport {\n  type HMR_ACTION_TYPES,\n  HMR_ACTIONS_SENT_TO_BROWSER,\n} from './hot-reloader-types'\nimport * as Log from '../../build/output/log'\nimport type { PropagateToWorkersField } from '../lib/router-utils/types'\nimport type { TurbopackManifestLoader } from '../../shared/lib/turbopack/manifest-loader'\nimport type { AppRoute, Entrypoints, PageRoute } from '../../build/swc/types'\nimport {\n  type EntryKey,\n  getEntryKey,\n  splitEntryKey,\n} from '../../shared/lib/turbopack/entry-key'\nimport type ws from 'next/dist/compiled/ws'\nimport { isMetadataRoute } from '../../lib/metadata/is-metadata-route'\nimport type { CustomRoutes } from '../../lib/load-custom-routes'\nimport {\n  formatIssue,\n  getIssueKey,\n  isRelevantWarning,\n  processIssues,\n  renderStyledStringToErrorAnsi,\n  type EntryIssuesMap,\n  type TopLevelIssuesMap,\n} from '../../shared/lib/turbopack/utils'\n\nconst onceErrorSet = new Set()\n/**\n * Check if given issue is a warning to be display only once.\n * This mimics behavior of get-page-static-info's warnOnce.\n * @param issue\n * @returns\n */\nfunction shouldEmitOnceWarning(issue: Issue): boolean {\n  const { severity, title, stage } = issue\n  if (severity === 'warning' && title.value === 'Invalid page configuration') {\n    if (onceErrorSet.has(issue)) {\n      return false\n    }\n    onceErrorSet.add(issue)\n  }\n  if (\n    severity === 'warning' &&\n    stage === 'config' &&\n    renderStyledStringToErrorAnsi(issue.title).includes(\"can't be external\")\n  ) {\n    if (onceErrorSet.has(issue)) {\n      return false\n    }\n    onceErrorSet.add(issue)\n  }\n\n  return true\n}\n\n/// Print out an issue to the console which should not block\n/// the build by throwing out or blocking error overlay.\nexport function printNonFatalIssue(issue: Issue) {\n  if (isRelevantWarning(issue) && shouldEmitOnceWarning(issue)) {\n    Log.warn(formatIssue(issue))\n  }\n}\n\nexport function processTopLevelIssues(\n  currentTopLevelIssues: TopLevelIssuesMap,\n  result: TurbopackResult\n) {\n  currentTopLevelIssues.clear()\n\n  for (const issue of result.issues) {\n    const issueKey = getIssueKey(issue)\n    currentTopLevelIssues.set(issueKey, issue)\n  }\n}\n\nconst MILLISECONDS_IN_NANOSECOND = BigInt(1_000_000)\n\nexport function msToNs(ms: number): bigint {\n  return BigInt(Math.floor(ms)) * MILLISECONDS_IN_NANOSECOND\n}\n\nexport type ChangeSubscriptions = Map<\n  EntryKey,\n  Promise<AsyncIterableIterator<TurbopackResult>>\n>\n\nexport type HandleWrittenEndpoint = (\n  key: EntryKey,\n  result: TurbopackResult<WrittenEndpoint>\n) => void\n\nexport type StartChangeSubscription = (\n  key: EntryKey,\n  includeIssues: boolean,\n  endpoint: Endpoint,\n  makePayload: (\n    change: TurbopackResult,\n    hash: string\n  ) => Promise<HMR_ACTION_TYPES> | HMR_ACTION_TYPES | void,\n  onError?: (e: Error) => Promise<HMR_ACTION_TYPES> | HMR_ACTION_TYPES | void\n) => Promise<void>\n\nexport type StopChangeSubscription = (key: EntryKey) => Promise<void>\n\nexport type SendHmr = (id: string, payload: HMR_ACTION_TYPES) => void\n\nexport type StartBuilding = (\n  id: string,\n  requestUrl: string | undefined,\n  forceRebuild: boolean\n) => () => void\n\nexport type ReadyIds = Set<string>\n\nexport type ClientState = {\n  clientIssues: EntryIssuesMap\n  hmrPayloads: Map<string, HMR_ACTION_TYPES>\n  turbopackUpdates: TurbopackUpdate[]\n  subscriptions: Map<string, AsyncIterator<any>>\n}\n\nexport type ClientStateMap = WeakMap<ws, ClientState>\n\n// hooks only used by the dev server.\ntype HandleRouteTypeHooks = {\n  handleWrittenEndpoint: HandleWrittenEndpoint\n  subscribeToChanges: StartChangeSubscription\n}\n\nexport async function handleRouteType({\n  dev,\n  page,\n  pathname,\n  route,\n  currentEntryIssues,\n  entrypoints,\n  manifestLoader,\n  readyIds,\n  devRewrites,\n  productionRewrites,\n  hooks,\n  logErrors,\n}: {\n  dev: boolean\n  page: string\n  pathname: string\n  route: PageRoute | AppRoute\n\n  currentEntryIssues: EntryIssuesMap\n  entrypoints: Entrypoints\n  manifestLoader: TurbopackManifestLoader\n  devRewrites: SetupOpts['fsChecker']['rewrites'] | undefined\n  productionRewrites: CustomRoutes['rewrites'] | undefined\n  logErrors: boolean\n\n  readyIds?: ReadyIds // dev\n\n  hooks?: HandleRouteTypeHooks // dev\n}) {\n  const shouldCreateWebpackStats = process.env.TURBOPACK_STATS != null\n\n  switch (route.type) {\n    case 'page': {\n      const clientKey = getEntryKey('pages', 'client', page)\n      const serverKey = getEntryKey('pages', 'server', page)\n\n      try {\n        if (entrypoints.global.app) {\n          const key = getEntryKey('pages', 'server', '_app')\n\n          const writtenEndpoint = await entrypoints.global.app.writeToDisk()\n          hooks?.handleWrittenEndpoint(key, writtenEndpoint)\n          processIssues(\n            currentEntryIssues,\n            key,\n            writtenEndpoint,\n            false,\n            logErrors\n          )\n        }\n        await manifestLoader.loadBuildManifest('_app')\n        await manifestLoader.loadPagesManifest('_app')\n\n        if (entrypoints.global.document) {\n          const key = getEntryKey('pages', 'server', '_document')\n\n          const writtenEndpoint =\n            await entrypoints.global.document.writeToDisk()\n          hooks?.handleWrittenEndpoint(key, writtenEndpoint)\n          processIssues(\n            currentEntryIssues,\n            key,\n            writtenEndpoint,\n            false,\n            logErrors\n          )\n        }\n        await manifestLoader.loadPagesManifest('_document')\n\n        const writtenEndpoint = await route.htmlEndpoint.writeToDisk()\n        hooks?.handleWrittenEndpoint(serverKey, writtenEndpoint)\n\n        const type = writtenEndpoint?.type\n\n        await manifestLoader.loadBuildManifest(page)\n        await manifestLoader.loadPagesManifest(page)\n        if (type === 'edge') {\n          await manifestLoader.loadMiddlewareManifest(page, 'pages')\n        } else {\n          manifestLoader.deleteMiddlewareManifest(serverKey)\n        }\n        await manifestLoader.loadFontManifest('/_app', 'pages')\n        await manifestLoader.loadFontManifest(page, 'pages')\n\n        if (shouldCreateWebpackStats) {\n          await manifestLoader.loadWebpackStats(page, 'pages')\n        }\n\n        await manifestLoader.writeManifests({\n          devRewrites,\n          productionRewrites,\n          entrypoints,\n        })\n\n        processIssues(\n          currentEntryIssues,\n          serverKey,\n          writtenEndpoint,\n          false,\n          logErrors\n        )\n      } finally {\n        if (dev) {\n          // TODO subscriptions should only be caused by the WebSocket connections\n          // otherwise we don't known when to unsubscribe and this leaking\n          hooks?.subscribeToChanges(\n            serverKey,\n            false,\n            route.dataEndpoint,\n            () => {\n              // Report the next compilation again\n              readyIds?.delete(pathname)\n              return {\n                event: HMR_ACTIONS_SENT_TO_BROWSER.SERVER_ONLY_CHANGES,\n                pages: [page],\n              }\n            },\n            (e) => {\n              return {\n                action: HMR_ACTIONS_SENT_TO_BROWSER.RELOAD_PAGE,\n                data: `error in ${page} data subscription: ${e}`,\n              }\n            }\n          )\n          hooks?.subscribeToChanges(\n            clientKey,\n            false,\n            route.htmlEndpoint,\n            () => {\n              return {\n                event: HMR_ACTIONS_SENT_TO_BROWSER.CLIENT_CHANGES,\n              }\n            },\n            (e) => {\n              return {\n                action: HMR_ACTIONS_SENT_TO_BROWSER.RELOAD_PAGE,\n                data: `error in ${page} html subscription: ${e}`,\n              }\n            }\n          )\n          if (entrypoints.global.document) {\n            hooks?.subscribeToChanges(\n              getEntryKey('pages', 'server', '_document'),\n              false,\n              entrypoints.global.document,\n              () => {\n                return {\n                  action: HMR_ACTIONS_SENT_TO_BROWSER.RELOAD_PAGE,\n                  data: '_document has changed (page route)',\n                }\n              },\n              (e) => {\n                return {\n                  action: HMR_ACTIONS_SENT_TO_BROWSER.RELOAD_PAGE,\n                  data: `error in _document subscription (page route): ${e}`,\n                }\n              }\n            )\n          }\n        }\n      }\n\n      break\n    }\n    case 'page-api': {\n      const key = getEntryKey('pages', 'server', page)\n\n      const writtenEndpoint = await route.endpoint.writeToDisk()\n      hooks?.handleWrittenEndpoint(key, writtenEndpoint)\n\n      const type = writtenEndpoint.type\n\n      await manifestLoader.loadPagesManifest(page)\n      if (type === 'edge') {\n        await manifestLoader.loadMiddlewareManifest(page, 'pages')\n      } else {\n        manifestLoader.deleteMiddlewareManifest(key)\n      }\n\n      await manifestLoader.writeManifests({\n        devRewrites,\n        productionRewrites,\n        entrypoints,\n      })\n\n      processIssues(currentEntryIssues, key, writtenEndpoint, true, logErrors)\n\n      break\n    }\n    case 'app-page': {\n      const key = getEntryKey('app', 'server', page)\n\n      const writtenEndpoint = await route.htmlEndpoint.writeToDisk()\n      hooks?.handleWrittenEndpoint(key, writtenEndpoint)\n\n      if (dev) {\n        // TODO subscriptions should only be caused by the WebSocket connections\n        // otherwise we don't known when to unsubscribe and this leaking\n        hooks?.subscribeToChanges(\n          key,\n          true,\n          route.rscEndpoint,\n          (change, hash) => {\n            if (change.issues.some((issue) => issue.severity === 'error')) {\n              // Ignore any updates that has errors\n              // There will be another update without errors eventually\n              return\n            }\n            // Report the next compilation again\n            readyIds?.delete(pathname)\n            return {\n              action: HMR_ACTIONS_SENT_TO_BROWSER.SERVER_COMPONENT_CHANGES,\n              hash,\n            }\n          },\n          (e) => {\n            return {\n              action: HMR_ACTIONS_SENT_TO_BROWSER.RELOAD_PAGE,\n              data: `error in ${page} app-page subscription: ${e}`,\n            }\n          }\n        )\n      }\n\n      const type = writtenEndpoint.type\n\n      if (type === 'edge') {\n        await manifestLoader.loadMiddlewareManifest(page, 'app')\n      } else {\n        manifestLoader.deleteMiddlewareManifest(key)\n      }\n\n      await manifestLoader.loadAppBuildManifest(page)\n      await manifestLoader.loadBuildManifest(page, 'app')\n      await manifestLoader.loadAppPathsManifest(page)\n      await manifestLoader.loadActionManifest(page)\n      await manifestLoader.loadFontManifest(page, 'app')\n\n      if (shouldCreateWebpackStats) {\n        await manifestLoader.loadWebpackStats(page, 'app')\n      }\n\n      await manifestLoader.writeManifests({\n        devRewrites,\n        productionRewrites,\n        entrypoints,\n      })\n\n      processIssues(currentEntryIssues, key, writtenEndpoint, dev, logErrors)\n\n      break\n    }\n    case 'app-route': {\n      const key = getEntryKey('app', 'server', page)\n\n      const writtenEndpoint = await route.endpoint.writeToDisk()\n      hooks?.handleWrittenEndpoint(key, writtenEndpoint)\n\n      const type = writtenEndpoint.type\n\n      await manifestLoader.loadAppPathsManifest(page)\n\n      if (type === 'edge') {\n        await manifestLoader.loadMiddlewareManifest(page, 'app')\n      } else {\n        manifestLoader.deleteMiddlewareManifest(key)\n      }\n\n      await manifestLoader.writeManifests({\n        devRewrites,\n        productionRewrites,\n        entrypoints,\n      })\n      processIssues(currentEntryIssues, key, writtenEndpoint, true, logErrors)\n\n      break\n    }\n    default: {\n      throw new Error(`unknown route type ${(route as any).type} for ${page}`)\n    }\n  }\n}\n\n/**\n * Maintains a mapping between entrypoins and the corresponding client asset paths.\n */\nexport class AssetMapper {\n  private entryMap: Map<EntryKey, Set<string>> = new Map()\n  private assetMap: Map<string, Set<EntryKey>> = new Map()\n\n  /**\n   * Overrides asset paths for a key and updates the mapping from path to key.\n   *\n   * @param key\n   * @param assetPaths asset paths relative to the .next directory\n   */\n  setPathsForKey(key: EntryKey, assetPaths: string[]): void {\n    this.delete(key)\n\n    const newAssetPaths = new Set(assetPaths)\n    this.entryMap.set(key, newAssetPaths)\n\n    for (const assetPath of newAssetPaths) {\n      let assetPathKeys = this.assetMap.get(assetPath)\n      if (!assetPathKeys) {\n        assetPathKeys = new Set()\n        this.assetMap.set(assetPath, assetPathKeys)\n      }\n\n      assetPathKeys!.add(key)\n    }\n  }\n\n  /**\n   * Deletes the key and any asset only referenced by this key.\n   *\n   * @param key\n   */\n  delete(key: EntryKey) {\n    for (const assetPath of this.getAssetPathsByKey(key)) {\n      const assetPathKeys = this.assetMap.get(assetPath)\n\n      assetPathKeys?.delete(key)\n\n      if (!assetPathKeys?.size) {\n        this.assetMap.delete(assetPath)\n      }\n    }\n\n    this.entryMap.delete(key)\n  }\n\n  getAssetPathsByKey(key: EntryKey): string[] {\n    return Array.from(this.entryMap.get(key) ?? [])\n  }\n\n  getKeysByAsset(path: string): EntryKey[] {\n    return Array.from(this.assetMap.get(path) ?? [])\n  }\n\n  keys(): IterableIterator<EntryKey> {\n    return this.entryMap.keys()\n  }\n}\n\nexport function hasEntrypointForKey(\n  entrypoints: Entrypoints,\n  key: EntryKey,\n  assetMapper: AssetMapper | undefined\n): boolean {\n  const { type, page } = splitEntryKey(key)\n\n  switch (type) {\n    case 'app':\n      return entrypoints.app.has(page)\n    case 'pages':\n      switch (page) {\n        case '_app':\n          return entrypoints.global.app != null\n        case '_document':\n          return entrypoints.global.document != null\n        case '_error':\n          return entrypoints.global.error != null\n        default:\n          return entrypoints.page.has(page)\n      }\n    case 'root':\n      switch (page) {\n        case 'middleware':\n          return entrypoints.global.middleware != null\n        case 'instrumentation':\n          return entrypoints.global.instrumentation != null\n        default:\n          return false\n      }\n    case 'assets':\n      if (!assetMapper) {\n        return false\n      }\n\n      return assetMapper\n        .getKeysByAsset(page)\n        .some((pageKey) =>\n          hasEntrypointForKey(entrypoints, pageKey, assetMapper)\n        )\n    default: {\n      // validation that we covered all cases, this should never run.\n      // eslint-disable-next-line @typescript-eslint/no-unused-vars\n      const _: never = type\n      return false\n    }\n  }\n}\n\n// hooks only used by the dev server.\ntype HandleEntrypointsHooks = {\n  handleWrittenEndpoint: HandleWrittenEndpoint\n  propagateServerField: (\n    field: PropagateToWorkersField,\n    args: any\n  ) => Promise<void>\n  sendHmr: SendHmr\n  startBuilding: StartBuilding\n  subscribeToChanges: StartChangeSubscription\n  unsubscribeFromChanges: StopChangeSubscription\n  unsubscribeFromHmrEvents: (client: ws, id: string) => void\n}\n\ntype HandleEntrypointsDevOpts = {\n  assetMapper: AssetMapper\n  changeSubscriptions: ChangeSubscriptions\n  clients: Set<ws>\n  clientStates: ClientStateMap\n  serverFields: ServerFields\n\n  hooks: HandleEntrypointsHooks\n}\n\nexport async function handleEntrypoints({\n  entrypoints,\n\n  currentEntrypoints,\n\n  currentEntryIssues,\n  manifestLoader,\n  devRewrites,\n  logErrors,\n  dev,\n}: {\n  entrypoints: TurbopackResult<RawEntrypoints>\n\n  currentEntrypoints: Entrypoints\n\n  currentEntryIssues: EntryIssuesMap\n  manifestLoader: TurbopackManifestLoader\n  devRewrites: SetupOpts['fsChecker']['rewrites'] | undefined\n  productionRewrites: CustomRoutes['rewrites'] | undefined\n  logErrors: boolean\n\n  dev: HandleEntrypointsDevOpts\n}) {\n  currentEntrypoints.global.app = entrypoints.pagesAppEndpoint\n  currentEntrypoints.global.document = entrypoints.pagesDocumentEndpoint\n  currentEntrypoints.global.error = entrypoints.pagesErrorEndpoint\n\n  currentEntrypoints.global.instrumentation = entrypoints.instrumentation\n\n  currentEntrypoints.page.clear()\n  currentEntrypoints.app.clear()\n\n  for (const [pathname, route] of entrypoints.routes) {\n    switch (route.type) {\n      case 'page':\n      case 'page-api':\n        currentEntrypoints.page.set(pathname, route)\n        break\n      case 'app-page': {\n        route.pages.forEach((page) => {\n          currentEntrypoints.app.set(page.originalName, {\n            type: 'app-page',\n            ...page,\n          })\n        })\n        break\n      }\n      case 'app-route': {\n        currentEntrypoints.app.set(route.originalName, route)\n        break\n      }\n      default:\n        Log.info(`skipping ${pathname} (${route.type})`)\n        break\n    }\n  }\n\n  if (dev) {\n    await handleEntrypointsDevCleanup({\n      currentEntryIssues,\n      currentEntrypoints,\n\n      ...dev,\n    })\n  }\n\n  const { middleware, instrumentation } = entrypoints\n\n  // We check for explicit true/false, since it's initialized to\n  // undefined during the first loop (middlewareChanges event is\n  // unnecessary during the first serve)\n  if (currentEntrypoints.global.middleware && !middleware) {\n    const key = getEntryKey('root', 'server', 'middleware')\n    // Went from middleware to no middleware\n    await dev?.hooks.unsubscribeFromChanges(key)\n    currentEntryIssues.delete(key)\n    dev.hooks.sendHmr('middleware', {\n      event: HMR_ACTIONS_SENT_TO_BROWSER.MIDDLEWARE_CHANGES,\n    })\n  } else if (!currentEntrypoints.global.middleware && middleware) {\n    // Went from no middleware to middleware\n    dev.hooks.sendHmr('middleware', {\n      event: HMR_ACTIONS_SENT_TO_BROWSER.MIDDLEWARE_CHANGES,\n    })\n  }\n\n  currentEntrypoints.global.middleware = middleware\n\n  if (instrumentation) {\n    const processInstrumentation = async (\n      name: string,\n      prop: 'nodeJs' | 'edge'\n    ) => {\n      const key = getEntryKey('root', 'server', name)\n\n      const writtenEndpoint = await instrumentation[prop].writeToDisk()\n      dev.hooks.handleWrittenEndpoint(key, writtenEndpoint)\n      processIssues(currentEntryIssues, key, writtenEndpoint, false, logErrors)\n    }\n    await processInstrumentation('instrumentation.nodeJs', 'nodeJs')\n    await processInstrumentation('instrumentation.edge', 'edge')\n    await manifestLoader.loadMiddlewareManifest(\n      'instrumentation',\n      'instrumentation'\n    )\n    await manifestLoader.writeManifests({\n      devRewrites,\n      productionRewrites: undefined,\n      entrypoints: currentEntrypoints,\n    })\n\n    dev.serverFields.actualInstrumentationHookFile = '/instrumentation'\n    await dev.hooks.propagateServerField(\n      'actualInstrumentationHookFile',\n      dev.serverFields.actualInstrumentationHookFile\n    )\n  } else {\n    dev.serverFields.actualInstrumentationHookFile = undefined\n    await dev.hooks.propagateServerField(\n      'actualInstrumentationHookFile',\n      dev.serverFields.actualInstrumentationHookFile\n    )\n  }\n\n  if (middleware) {\n    const key = getEntryKey('root', 'server', 'middleware')\n\n    const endpoint = middleware.endpoint\n\n    async function processMiddleware() {\n      const writtenEndpoint = await endpoint.writeToDisk()\n      dev.hooks.handleWrittenEndpoint(key, writtenEndpoint)\n      processIssues(currentEntryIssues, key, writtenEndpoint, false, logErrors)\n      await manifestLoader.loadMiddlewareManifest('middleware', 'middleware')\n      const middlewareConfig =\n        manifestLoader.getMiddlewareManifest(key)?.middleware['/']\n\n      if (dev && middlewareConfig) {\n        dev.serverFields.middleware = {\n          match: null as any,\n          page: '/',\n          matchers: middlewareConfig.matchers,\n        }\n      }\n    }\n    await processMiddleware()\n\n    if (dev) {\n      dev?.hooks.subscribeToChanges(\n        key,\n        false,\n        endpoint,\n        async () => {\n          const finishBuilding = dev.hooks.startBuilding(\n            'middleware',\n            undefined,\n            true\n          )\n          await processMiddleware()\n          await dev.hooks.propagateServerField(\n            'actualMiddlewareFile',\n            dev.serverFields.actualMiddlewareFile\n          )\n          await dev.hooks.propagateServerField(\n            'middleware',\n            dev.serverFields.middleware\n          )\n          await manifestLoader.writeManifests({\n            devRewrites,\n            productionRewrites: undefined,\n            entrypoints: currentEntrypoints,\n          })\n\n          finishBuilding?.()\n          return { event: HMR_ACTIONS_SENT_TO_BROWSER.MIDDLEWARE_CHANGES }\n        },\n        () => {\n          return {\n            event: HMR_ACTIONS_SENT_TO_BROWSER.MIDDLEWARE_CHANGES,\n          }\n        }\n      )\n    }\n  } else {\n    manifestLoader.deleteMiddlewareManifest(\n      getEntryKey('root', 'server', 'middleware')\n    )\n    dev.serverFields.actualMiddlewareFile = undefined\n    dev.serverFields.middleware = undefined\n  }\n\n  await dev.hooks.propagateServerField(\n    'actualMiddlewareFile',\n    dev.serverFields.actualMiddlewareFile\n  )\n  await dev.hooks.propagateServerField(\n    'middleware',\n    dev.serverFields.middleware\n  )\n}\n\nasync function handleEntrypointsDevCleanup({\n  currentEntryIssues,\n  currentEntrypoints,\n\n  assetMapper,\n  changeSubscriptions,\n  clients,\n  clientStates,\n\n  hooks,\n}: {\n  currentEntrypoints: Entrypoints\n  currentEntryIssues: EntryIssuesMap\n} & HandleEntrypointsDevOpts) {\n  // this needs to be first as `hasEntrypointForKey` uses the `assetMapper`\n  for (const key of assetMapper.keys()) {\n    if (!hasEntrypointForKey(currentEntrypoints, key, assetMapper)) {\n      assetMapper.delete(key)\n    }\n  }\n\n  for (const key of changeSubscriptions.keys()) {\n    // middleware is handled separately\n    if (!hasEntrypointForKey(currentEntrypoints, key, assetMapper)) {\n      await hooks.unsubscribeFromChanges(key)\n    }\n  }\n\n  for (const [key] of currentEntryIssues) {\n    if (!hasEntrypointForKey(currentEntrypoints, key, assetMapper)) {\n      currentEntryIssues.delete(key)\n    }\n  }\n\n  for (const client of clients) {\n    const state = clientStates.get(client)\n    if (!state) {\n      continue\n    }\n\n    for (const key of state.clientIssues.keys()) {\n      if (!hasEntrypointForKey(currentEntrypoints, key, assetMapper)) {\n        state.clientIssues.delete(key)\n      }\n    }\n\n    for (const id of state.subscriptions.keys()) {\n      if (\n        !hasEntrypointForKey(\n          currentEntrypoints,\n          getEntryKey('assets', 'client', id),\n          assetMapper\n        )\n      ) {\n        hooks.unsubscribeFromHmrEvents(client, id)\n      }\n    }\n  }\n}\n\nexport async function handlePagesErrorRoute({\n  currentEntryIssues,\n  entrypoints,\n  manifestLoader,\n  devRewrites,\n  productionRewrites,\n  logErrors,\n  hooks,\n}: {\n  currentEntryIssues: EntryIssuesMap\n  entrypoints: Entrypoints\n  manifestLoader: TurbopackManifestLoader\n  devRewrites: SetupOpts['fsChecker']['rewrites'] | undefined\n  productionRewrites: CustomRoutes['rewrites'] | undefined\n  logErrors: boolean\n  hooks: HandleRouteTypeHooks\n}) {\n  if (entrypoints.global.app) {\n    const key = getEntryKey('pages', 'server', '_app')\n\n    const writtenEndpoint = await entrypoints.global.app.writeToDisk()\n    hooks.handleWrittenEndpoint(key, writtenEndpoint)\n    hooks.subscribeToChanges(\n      key,\n      false,\n      entrypoints.global.app,\n      () => {\n        // There's a special case for this in `../client/page-bootstrap.ts`.\n        // https://github.com/vercel/next.js/blob/08d7a7e5189a835f5dcb82af026174e587575c0e/packages/next/src/client/page-bootstrap.ts#L69-L71\n        return { event: HMR_ACTIONS_SENT_TO_BROWSER.CLIENT_CHANGES }\n      },\n      () => {\n        return {\n          action: HMR_ACTIONS_SENT_TO_BROWSER.RELOAD_PAGE,\n          data: '_app has changed (error route)',\n        }\n      }\n    )\n    processIssues(currentEntryIssues, key, writtenEndpoint, false, logErrors)\n  }\n  await manifestLoader.loadBuildManifest('_app')\n  await manifestLoader.loadPagesManifest('_app')\n  await manifestLoader.loadFontManifest('_app')\n\n  if (entrypoints.global.document) {\n    const key = getEntryKey('pages', 'server', '_document')\n\n    const writtenEndpoint = await entrypoints.global.document.writeToDisk()\n    hooks.handleWrittenEndpoint(key, writtenEndpoint)\n    hooks.subscribeToChanges(\n      key,\n      false,\n      entrypoints.global.document,\n      () => {\n        return {\n          action: HMR_ACTIONS_SENT_TO_BROWSER.RELOAD_PAGE,\n          data: '_document has changed (error route)',\n        }\n      },\n      (e) => {\n        return {\n          action: HMR_ACTIONS_SENT_TO_BROWSER.RELOAD_PAGE,\n          data: `error in _document subscription (error route): ${e}`,\n        }\n      }\n    )\n    processIssues(currentEntryIssues, key, writtenEndpoint, false, logErrors)\n  }\n  await manifestLoader.loadPagesManifest('_document')\n\n  if (entrypoints.global.error) {\n    const key = getEntryKey('pages', 'server', '_error')\n\n    const writtenEndpoint = await entrypoints.global.error.writeToDisk()\n    hooks.handleWrittenEndpoint(key, writtenEndpoint)\n    hooks.subscribeToChanges(\n      key,\n      false,\n      entrypoints.global.error,\n      () => {\n        // There's a special case for this in `../client/page-bootstrap.ts`.\n        // https://github.com/vercel/next.js/blob/08d7a7e5189a835f5dcb82af026174e587575c0e/packages/next/src/client/page-bootstrap.ts#L69-L71\n        return { event: HMR_ACTIONS_SENT_TO_BROWSER.CLIENT_CHANGES }\n      },\n      (e) => {\n        return {\n          action: HMR_ACTIONS_SENT_TO_BROWSER.RELOAD_PAGE,\n          data: `error in _error subscription: ${e}`,\n        }\n      }\n    )\n    processIssues(currentEntryIssues, key, writtenEndpoint, false, logErrors)\n  }\n  await manifestLoader.loadBuildManifest('_error')\n  await manifestLoader.loadPagesManifest('_error')\n  await manifestLoader.loadFontManifest('_error')\n\n  await manifestLoader.writeManifests({\n    devRewrites,\n    productionRewrites,\n    entrypoints,\n  })\n}\n\nexport function removeRouteSuffix(route: string): string {\n  return route.replace(/\\/route$/, '')\n}\n\nexport function addRouteSuffix(route: string): string {\n  return route + '/route'\n}\n\nexport function addMetadataIdToRoute(route: string): string {\n  return route + '/[__metadata_id__]'\n}\n\n// Since turbopack will create app pages/route entries based on the structure,\n// which means the entry keys are based on file names.\n// But for special metadata conventions we'll change the page/pathname to a different path.\n// So we need this helper to map the new path back to original turbopack entry key.\nexport function normalizedPageToTurbopackStructureRoute(\n  route: string,\n  ext: string | false\n): string {\n  let entrypointKey = route\n  if (isMetadataRoute(entrypointKey)) {\n    entrypointKey = entrypointKey.endsWith('/route')\n      ? entrypointKey.slice(0, -'/route'.length)\n      : entrypointKey\n\n    if (ext) {\n      if (entrypointKey.endsWith('/[__metadata_id__]')) {\n        entrypointKey = entrypointKey.slice(0, -'/[__metadata_id__]'.length)\n      }\n      if (entrypointKey.endsWith('/sitemap.xml') && ext !== '.xml') {\n        // For dynamic sitemap route, remove the extension\n        entrypointKey = entrypointKey.slice(0, -'.xml'.length)\n      }\n    }\n    entrypointKey = entrypointKey + '/route'\n  }\n  return entrypointKey\n}\n"], "names": ["AssetMapper", "addMetadataIdToRoute", "addRouteSuffix", "handleEntrypoints", "handlePagesErrorRoute", "handleRouteType", "hasEntrypointForKey", "msToNs", "normalizedPageToTurbopackStructureRoute", "printNonFatalIssue", "processTopLevelIssues", "removeRouteSuffix", "onceErrorSet", "Set", "shouldEmitOnceWarning", "issue", "severity", "title", "stage", "value", "has", "add", "renderStyledStringToErrorAnsi", "includes", "isRelevantWarning", "Log", "warn", "formatIssue", "currentTopLevelIssues", "result", "clear", "issues", "issue<PERSON><PERSON>", "getIssueKey", "set", "MILLISECONDS_IN_NANOSECOND", "BigInt", "ms", "Math", "floor", "dev", "page", "pathname", "route", "currentEntryIssues", "entrypoints", "manifest<PERSON><PERSON>der", "readyIds", "devRewrites", "productionRewrites", "hooks", "logErrors", "shouldCreateWebpackStats", "process", "env", "TURBOPACK_STATS", "type", "client<PERSON>ey", "getEntry<PERSON>ey", "server<PERSON>ey", "global", "app", "key", "writtenEndpoint", "writeToDisk", "handleWrittenEndpoint", "processIssues", "loadBuildManifest", "loadPagesManifest", "document", "htmlEndpoint", "loadMiddlewareManifest", "deleteMiddlewareManifest", "loadFontManifest", "loadWebpackStats", "writeManifests", "subscribeToChanges", "dataEndpoint", "delete", "event", "HMR_ACTIONS_SENT_TO_BROWSER", "SERVER_ONLY_CHANGES", "pages", "e", "action", "RELOAD_PAGE", "data", "CLIENT_CHANGES", "endpoint", "rscEndpoint", "change", "hash", "some", "SERVER_COMPONENT_CHANGES", "loadAppBuildManifest", "loadAppPathsManifest", "loadActionManifest", "Error", "setPathsFor<PERSON>ey", "assetPaths", "newAssetPaths", "entryMap", "assetPath", "assetPathKeys", "assetMap", "get", "getAssetPathsByKey", "size", "Array", "from", "getKeysByAsset", "path", "keys", "Map", "assetMapper", "splitEntryKey", "error", "middleware", "instrumentation", "page<PERSON><PERSON>", "_", "currentEntrypoints", "pagesAppEndpoint", "pagesDocumentEndpoint", "pagesErrorEndpoint", "routes", "for<PERSON>ach", "originalName", "info", "handleEntrypointsDevCleanup", "unsubscribeFromChanges", "sendHmr", "MIDDLEWARE_CHANGES", "processInstrumentation", "name", "prop", "undefined", "serverFields", "actualInstrumentationHookFile", "propagateServerField", "processMiddleware", "middlewareConfig", "getMiddlewareManifest", "match", "matchers", "finishBuilding", "startBuilding", "actualMiddlewareFile", "changeSubscriptions", "clients", "clientStates", "client", "state", "clientIssues", "id", "subscriptions", "unsubscribeFromHmrEvents", "replace", "ext", "entrypoint<PERSON><PERSON>", "isMetadataRoute", "endsWith", "slice", "length"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;IA4aaA,WAAW;eAAXA;;IAyfGC,oBAAoB;eAApBA;;IAJAC,cAAc;eAAdA;;IAjXMC,iBAAiB;eAAjBA;;IAqQAC,qBAAqB;eAArBA;;IAxqBAC,eAAe;eAAfA;;IA0VNC,mBAAmB;eAAnBA;;IA9YAC,MAAM;eAANA;;IAo1BAC,uCAAuC;eAAvCA;;IAx2BAC,kBAAkB;eAAlBA;;IAMAC,qBAAqB;eAArBA;;IAk1BAC,iBAAiB;eAAjBA;;;kCA94BT;6DACc;0BAQd;iCAEyB;uBAUzB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEP,MAAMC,eAAe,IAAIC;AACzB;;;;;CAKC,GACD,SAASC,sBAAsBC,KAAY;IACzC,MAAM,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,KAAK,EAAE,GAAGH;IACnC,IAAIC,aAAa,aAAaC,MAAME,KAAK,KAAK,8BAA8B;QAC1E,IAAIP,aAAaQ,GAAG,CAACL,QAAQ;YAC3B,OAAO;QACT;QACAH,aAAaS,GAAG,CAACN;IACnB;IACA,IACEC,aAAa,aACbE,UAAU,YACVI,IAAAA,oCAA6B,EAACP,MAAME,KAAK,EAAEM,QAAQ,CAAC,sBACpD;QACA,IAAIX,aAAaQ,GAAG,CAACL,QAAQ;YAC3B,OAAO;QACT;QACAH,aAAaS,GAAG,CAACN;IACnB;IAEA,OAAO;AACT;AAIO,SAASN,mBAAmBM,KAAY;IAC7C,IAAIS,IAAAA,wBAAiB,EAACT,UAAUD,sBAAsBC,QAAQ;QAC5DU,KAAIC,IAAI,CAACC,IAAAA,kBAAW,EAACZ;IACvB;AACF;AAEO,SAASL,sBACdkB,qBAAwC,EACxCC,MAAuB;IAEvBD,sBAAsBE,KAAK;IAE3B,KAAK,MAAMf,SAASc,OAAOE,MAAM,CAAE;QACjC,MAAMC,WAAWC,IAAAA,kBAAW,EAAClB;QAC7Ba,sBAAsBM,GAAG,CAACF,UAAUjB;IACtC;AACF;AAEA,MAAMoB,6BAA6BC,OAAO;AAEnC,SAAS7B,OAAO8B,EAAU;IAC/B,OAAOD,OAAOE,KAAKC,KAAK,CAACF,OAAOF;AAClC;AAkDO,eAAe9B,gBAAgB,EACpCmC,GAAG,EACHC,IAAI,EACJC,QAAQ,EACRC,KAAK,EACLC,kBAAkB,EAClBC,WAAW,EACXC,cAAc,EACdC,QAAQ,EACRC,WAAW,EACXC,kBAAkB,EAClBC,KAAK,EACLC,SAAS,EAiBV;IACC,MAAMC,2BAA2BC,QAAQC,GAAG,CAACC,eAAe,IAAI;IAEhE,OAAQZ,MAAMa,IAAI;QAChB,KAAK;YAAQ;gBACX,MAAMC,YAAYC,IAAAA,qBAAW,EAAC,SAAS,UAAUjB;gBACjD,MAAMkB,YAAYD,IAAAA,qBAAW,EAAC,SAAS,UAAUjB;gBAEjD,IAAI;oBACF,IAAII,YAAYe,MAAM,CAACC,GAAG,EAAE;wBAC1B,MAAMC,MAAMJ,IAAAA,qBAAW,EAAC,SAAS,UAAU;wBAE3C,MAAMK,kBAAkB,MAAMlB,YAAYe,MAAM,CAACC,GAAG,CAACG,WAAW;wBAChEd,yBAAAA,MAAOe,qBAAqB,CAACH,KAAKC;wBAClCG,IAAAA,oBAAa,EACXtB,oBACAkB,KACAC,iBACA,OACAZ;oBAEJ;oBACA,MAAML,eAAeqB,iBAAiB,CAAC;oBACvC,MAAMrB,eAAesB,iBAAiB,CAAC;oBAEvC,IAAIvB,YAAYe,MAAM,CAACS,QAAQ,EAAE;wBAC/B,MAAMP,MAAMJ,IAAAA,qBAAW,EAAC,SAAS,UAAU;wBAE3C,MAAMK,kBACJ,MAAMlB,YAAYe,MAAM,CAACS,QAAQ,CAACL,WAAW;wBAC/Cd,yBAAAA,MAAOe,qBAAqB,CAACH,KAAKC;wBAClCG,IAAAA,oBAAa,EACXtB,oBACAkB,KACAC,iBACA,OACAZ;oBAEJ;oBACA,MAAML,eAAesB,iBAAiB,CAAC;oBAEvC,MAAML,kBAAkB,MAAMpB,MAAM2B,YAAY,CAACN,WAAW;oBAC5Dd,yBAAAA,MAAOe,qBAAqB,CAACN,WAAWI;oBAExC,MAAMP,OAAOO,mCAAAA,gBAAiBP,IAAI;oBAElC,MAAMV,eAAeqB,iBAAiB,CAAC1B;oBACvC,MAAMK,eAAesB,iBAAiB,CAAC3B;oBACvC,IAAIe,SAAS,QAAQ;wBACnB,MAAMV,eAAeyB,sBAAsB,CAAC9B,MAAM;oBACpD,OAAO;wBACLK,eAAe0B,wBAAwB,CAACb;oBAC1C;oBACA,MAAMb,eAAe2B,gBAAgB,CAAC,SAAS;oBAC/C,MAAM3B,eAAe2B,gBAAgB,CAAChC,MAAM;oBAE5C,IAAIW,0BAA0B;wBAC5B,MAAMN,eAAe4B,gBAAgB,CAACjC,MAAM;oBAC9C;oBAEA,MAAMK,eAAe6B,cAAc,CAAC;wBAClC3B;wBACAC;wBACAJ;oBACF;oBAEAqB,IAAAA,oBAAa,EACXtB,oBACAe,WACAI,iBACA,OACAZ;gBAEJ,SAAU;oBACR,IAAIX,KAAK;wBACP,wEAAwE;wBACxE,gEAAgE;wBAChEU,yBAAAA,MAAO0B,kBAAkB,CACvBjB,WACA,OACAhB,MAAMkC,YAAY,EAClB;4BACE,oCAAoC;4BACpC9B,4BAAAA,SAAU+B,MAAM,CAACpC;4BACjB,OAAO;gCACLqC,OAAOC,6CAA2B,CAACC,mBAAmB;gCACtDC,OAAO;oCAACzC;iCAAK;4BACf;wBACF,GACA,CAAC0C;4BACC,OAAO;gCACLC,QAAQJ,6CAA2B,CAACK,WAAW;gCAC/CC,MAAM,CAAC,SAAS,EAAE7C,KAAK,oBAAoB,EAAE0C,GAAG;4BAClD;wBACF;wBAEFjC,yBAAAA,MAAO0B,kBAAkB,CACvBnB,WACA,OACAd,MAAM2B,YAAY,EAClB;4BACE,OAAO;gCACLS,OAAOC,6CAA2B,CAACO,cAAc;4BACnD;wBACF,GACA,CAACJ;4BACC,OAAO;gCACLC,QAAQJ,6CAA2B,CAACK,WAAW;gCAC/CC,MAAM,CAAC,SAAS,EAAE7C,KAAK,oBAAoB,EAAE0C,GAAG;4BAClD;wBACF;wBAEF,IAAItC,YAAYe,MAAM,CAACS,QAAQ,EAAE;4BAC/BnB,yBAAAA,MAAO0B,kBAAkB,CACvBlB,IAAAA,qBAAW,EAAC,SAAS,UAAU,cAC/B,OACAb,YAAYe,MAAM,CAACS,QAAQ,EAC3B;gCACE,OAAO;oCACLe,QAAQJ,6CAA2B,CAACK,WAAW;oCAC/CC,MAAM;gCACR;4BACF,GACA,CAACH;gCACC,OAAO;oCACLC,QAAQJ,6CAA2B,CAACK,WAAW;oCAC/CC,MAAM,CAAC,8CAA8C,EAAEH,GAAG;gCAC5D;4BACF;wBAEJ;oBACF;gBACF;gBAEA;YACF;QACA,KAAK;YAAY;gBACf,MAAMrB,MAAMJ,IAAAA,qBAAW,EAAC,SAAS,UAAUjB;gBAE3C,MAAMsB,kBAAkB,MAAMpB,MAAM6C,QAAQ,CAACxB,WAAW;gBACxDd,yBAAAA,MAAOe,qBAAqB,CAACH,KAAKC;gBAElC,MAAMP,OAAOO,gBAAgBP,IAAI;gBAEjC,MAAMV,eAAesB,iBAAiB,CAAC3B;gBACvC,IAAIe,SAAS,QAAQ;oBACnB,MAAMV,eAAeyB,sBAAsB,CAAC9B,MAAM;gBACpD,OAAO;oBACLK,eAAe0B,wBAAwB,CAACV;gBAC1C;gBAEA,MAAMhB,eAAe6B,cAAc,CAAC;oBAClC3B;oBACAC;oBACAJ;gBACF;gBAEAqB,IAAAA,oBAAa,EAACtB,oBAAoBkB,KAAKC,iBAAiB,MAAMZ;gBAE9D;YACF;QACA,KAAK;YAAY;gBACf,MAAMW,MAAMJ,IAAAA,qBAAW,EAAC,OAAO,UAAUjB;gBAEzC,MAAMsB,kBAAkB,MAAMpB,MAAM2B,YAAY,CAACN,WAAW;gBAC5Dd,yBAAAA,MAAOe,qBAAqB,CAACH,KAAKC;gBAElC,IAAIvB,KAAK;oBACP,wEAAwE;oBACxE,gEAAgE;oBAChEU,yBAAAA,MAAO0B,kBAAkB,CACvBd,KACA,MACAnB,MAAM8C,WAAW,EACjB,CAACC,QAAQC;wBACP,IAAID,OAAO3D,MAAM,CAAC6D,IAAI,CAAC,CAAC7E,QAAUA,MAAMC,QAAQ,KAAK,UAAU;4BAC7D,qCAAqC;4BACrC,yDAAyD;4BACzD;wBACF;wBACA,oCAAoC;wBACpC+B,4BAAAA,SAAU+B,MAAM,CAACpC;wBACjB,OAAO;4BACL0C,QAAQJ,6CAA2B,CAACa,wBAAwB;4BAC5DF;wBACF;oBACF,GACA,CAACR;wBACC,OAAO;4BACLC,QAAQJ,6CAA2B,CAACK,WAAW;4BAC/CC,MAAM,CAAC,SAAS,EAAE7C,KAAK,wBAAwB,EAAE0C,GAAG;wBACtD;oBACF;gBAEJ;gBAEA,MAAM3B,OAAOO,gBAAgBP,IAAI;gBAEjC,IAAIA,SAAS,QAAQ;oBACnB,MAAMV,eAAeyB,sBAAsB,CAAC9B,MAAM;gBACpD,OAAO;oBACLK,eAAe0B,wBAAwB,CAACV;gBAC1C;gBAEA,MAAMhB,eAAegD,oBAAoB,CAACrD;gBAC1C,MAAMK,eAAeqB,iBAAiB,CAAC1B,MAAM;gBAC7C,MAAMK,eAAeiD,oBAAoB,CAACtD;gBAC1C,MAAMK,eAAekD,kBAAkB,CAACvD;gBACxC,MAAMK,eAAe2B,gBAAgB,CAAChC,MAAM;gBAE5C,IAAIW,0BAA0B;oBAC5B,MAAMN,eAAe4B,gBAAgB,CAACjC,MAAM;gBAC9C;gBAEA,MAAMK,eAAe6B,cAAc,CAAC;oBAClC3B;oBACAC;oBACAJ;gBACF;gBAEAqB,IAAAA,oBAAa,EAACtB,oBAAoBkB,KAAKC,iBAAiBvB,KAAKW;gBAE7D;YACF;QACA,KAAK;YAAa;gBAChB,MAAMW,MAAMJ,IAAAA,qBAAW,EAAC,OAAO,UAAUjB;gBAEzC,MAAMsB,kBAAkB,MAAMpB,MAAM6C,QAAQ,CAACxB,WAAW;gBACxDd,yBAAAA,MAAOe,qBAAqB,CAACH,KAAKC;gBAElC,MAAMP,OAAOO,gBAAgBP,IAAI;gBAEjC,MAAMV,eAAeiD,oBAAoB,CAACtD;gBAE1C,IAAIe,SAAS,QAAQ;oBACnB,MAAMV,eAAeyB,sBAAsB,CAAC9B,MAAM;gBACpD,OAAO;oBACLK,eAAe0B,wBAAwB,CAACV;gBAC1C;gBAEA,MAAMhB,eAAe6B,cAAc,CAAC;oBAClC3B;oBACAC;oBACAJ;gBACF;gBACAqB,IAAAA,oBAAa,EAACtB,oBAAoBkB,KAAKC,iBAAiB,MAAMZ;gBAE9D;YACF;QACA;YAAS;gBACP,MAAM,qBAAkE,CAAlE,IAAI8C,MAAM,CAAC,mBAAmB,EAAE,AAACtD,MAAca,IAAI,CAAC,KAAK,EAAEf,MAAM,GAAjE,qBAAA;2BAAA;gCAAA;kCAAA;gBAAiE;YACzE;IACF;AACF;AAKO,MAAMzC;IAIX;;;;;GAKC,GACDkG,eAAepC,GAAa,EAAEqC,UAAoB,EAAQ;QACxD,IAAI,CAACrB,MAAM,CAAChB;QAEZ,MAAMsC,gBAAgB,IAAIvF,IAAIsF;QAC9B,IAAI,CAACE,QAAQ,CAACnE,GAAG,CAAC4B,KAAKsC;QAEvB,KAAK,MAAME,aAAaF,cAAe;YACrC,IAAIG,gBAAgB,IAAI,CAACC,QAAQ,CAACC,GAAG,CAACH;YACtC,IAAI,CAACC,eAAe;gBAClBA,gBAAgB,IAAI1F;gBACpB,IAAI,CAAC2F,QAAQ,CAACtE,GAAG,CAACoE,WAAWC;YAC/B;YAEAA,cAAelF,GAAG,CAACyC;QACrB;IACF;IAEA;;;;GAIC,GACDgB,OAAOhB,GAAa,EAAE;QACpB,KAAK,MAAMwC,aAAa,IAAI,CAACI,kBAAkB,CAAC5C,KAAM;YACpD,MAAMyC,gBAAgB,IAAI,CAACC,QAAQ,CAACC,GAAG,CAACH;YAExCC,iCAAAA,cAAezB,MAAM,CAAChB;YAEtB,IAAI,EAACyC,iCAAAA,cAAeI,IAAI,GAAE;gBACxB,IAAI,CAACH,QAAQ,CAAC1B,MAAM,CAACwB;YACvB;QACF;QAEA,IAAI,CAACD,QAAQ,CAACvB,MAAM,CAAChB;IACvB;IAEA4C,mBAAmB5C,GAAa,EAAY;QAC1C,OAAO8C,MAAMC,IAAI,CAAC,IAAI,CAACR,QAAQ,CAACI,GAAG,CAAC3C,QAAQ,EAAE;IAChD;IAEAgD,eAAeC,IAAY,EAAc;QACvC,OAAOH,MAAMC,IAAI,CAAC,IAAI,CAACL,QAAQ,CAACC,GAAG,CAACM,SAAS,EAAE;IACjD;IAEAC,OAAmC;QACjC,OAAO,IAAI,CAACX,QAAQ,CAACW,IAAI;IAC3B;;aAvDQX,WAAuC,IAAIY;aAC3CT,WAAuC,IAAIS;;AAuDrD;AAEO,SAAS3G,oBACduC,WAAwB,EACxBiB,GAAa,EACboD,WAAoC;IAEpC,MAAM,EAAE1D,IAAI,EAAEf,IAAI,EAAE,GAAG0E,IAAAA,uBAAa,EAACrD;IAErC,OAAQN;QACN,KAAK;YACH,OAAOX,YAAYgB,GAAG,CAACzC,GAAG,CAACqB;QAC7B,KAAK;YACH,OAAQA;gBACN,KAAK;oBACH,OAAOI,YAAYe,MAAM,CAACC,GAAG,IAAI;gBACnC,KAAK;oBACH,OAAOhB,YAAYe,MAAM,CAACS,QAAQ,IAAI;gBACxC,KAAK;oBACH,OAAOxB,YAAYe,MAAM,CAACwD,KAAK,IAAI;gBACrC;oBACE,OAAOvE,YAAYJ,IAAI,CAACrB,GAAG,CAACqB;YAChC;QACF,KAAK;YACH,OAAQA;gBACN,KAAK;oBACH,OAAOI,YAAYe,MAAM,CAACyD,UAAU,IAAI;gBAC1C,KAAK;oBACH,OAAOxE,YAAYe,MAAM,CAAC0D,eAAe,IAAI;gBAC/C;oBACE,OAAO;YACX;QACF,KAAK;YACH,IAAI,CAACJ,aAAa;gBAChB,OAAO;YACT;YAEA,OAAOA,YACJJ,cAAc,CAACrE,MACfmD,IAAI,CAAC,CAAC2B,UACLjH,oBAAoBuC,aAAa0E,SAASL;QAEhD;YAAS;gBACP,+DAA+D;gBAC/D,6DAA6D;gBAC7D,MAAMM,IAAWhE;gBACjB,OAAO;YACT;IACF;AACF;AA0BO,eAAerD,kBAAkB,EACtC0C,WAAW,EAEX4E,kBAAkB,EAElB7E,kBAAkB,EAClBE,cAAc,EACdE,WAAW,EACXG,SAAS,EACTX,GAAG,EAaJ;IACCiF,mBAAmB7D,MAAM,CAACC,GAAG,GAAGhB,YAAY6E,gBAAgB;IAC5DD,mBAAmB7D,MAAM,CAACS,QAAQ,GAAGxB,YAAY8E,qBAAqB;IACtEF,mBAAmB7D,MAAM,CAACwD,KAAK,GAAGvE,YAAY+E,kBAAkB;IAEhEH,mBAAmB7D,MAAM,CAAC0D,eAAe,GAAGzE,YAAYyE,eAAe;IAEvEG,mBAAmBhF,IAAI,CAACX,KAAK;IAC7B2F,mBAAmB5D,GAAG,CAAC/B,KAAK;IAE5B,KAAK,MAAM,CAACY,UAAUC,MAAM,IAAIE,YAAYgF,MAAM,CAAE;QAClD,OAAQlF,MAAMa,IAAI;YAChB,KAAK;YACL,KAAK;gBACHiE,mBAAmBhF,IAAI,CAACP,GAAG,CAACQ,UAAUC;gBACtC;YACF,KAAK;gBAAY;oBACfA,MAAMuC,KAAK,CAAC4C,OAAO,CAAC,CAACrF;wBACnBgF,mBAAmB5D,GAAG,CAAC3B,GAAG,CAACO,KAAKsF,YAAY,EAAE;4BAC5CvE,MAAM;4BACN,GAAGf,IAAI;wBACT;oBACF;oBACA;gBACF;YACA,KAAK;gBAAa;oBAChBgF,mBAAmB5D,GAAG,CAAC3B,GAAG,CAACS,MAAMoF,YAAY,EAAEpF;oBAC/C;gBACF;YACA;gBACElB,KAAIuG,IAAI,CAAC,CAAC,SAAS,EAAEtF,SAAS,EAAE,EAAEC,MAAMa,IAAI,CAAC,CAAC,CAAC;gBAC/C;QACJ;IACF;IAEA,IAAIhB,KAAK;QACP,MAAMyF,4BAA4B;YAChCrF;YACA6E;YAEA,GAAGjF,GAAG;QACR;IACF;IAEA,MAAM,EAAE6E,UAAU,EAAEC,eAAe,EAAE,GAAGzE;IAExC,8DAA8D;IAC9D,8DAA8D;IAC9D,sCAAsC;IACtC,IAAI4E,mBAAmB7D,MAAM,CAACyD,UAAU,IAAI,CAACA,YAAY;QACvD,MAAMvD,MAAMJ,IAAAA,qBAAW,EAAC,QAAQ,UAAU;QAC1C,wCAAwC;QACxC,OAAMlB,uBAAAA,IAAKU,KAAK,CAACgF,sBAAsB,CAACpE;QACxClB,mBAAmBkC,MAAM,CAAChB;QAC1BtB,IAAIU,KAAK,CAACiF,OAAO,CAAC,cAAc;YAC9BpD,OAAOC,6CAA2B,CAACoD,kBAAkB;QACvD;IACF,OAAO,IAAI,CAACX,mBAAmB7D,MAAM,CAACyD,UAAU,IAAIA,YAAY;QAC9D,wCAAwC;QACxC7E,IAAIU,KAAK,CAACiF,OAAO,CAAC,cAAc;YAC9BpD,OAAOC,6CAA2B,CAACoD,kBAAkB;QACvD;IACF;IAEAX,mBAAmB7D,MAAM,CAACyD,UAAU,GAAGA;IAEvC,IAAIC,iBAAiB;QACnB,MAAMe,yBAAyB,OAC7BC,MACAC;YAEA,MAAMzE,MAAMJ,IAAAA,qBAAW,EAAC,QAAQ,UAAU4E;YAE1C,MAAMvE,kBAAkB,MAAMuD,eAAe,CAACiB,KAAK,CAACvE,WAAW;YAC/DxB,IAAIU,KAAK,CAACe,qBAAqB,CAACH,KAAKC;YACrCG,IAAAA,oBAAa,EAACtB,oBAAoBkB,KAAKC,iBAAiB,OAAOZ;QACjE;QACA,MAAMkF,uBAAuB,0BAA0B;QACvD,MAAMA,uBAAuB,wBAAwB;QACrD,MAAMvF,eAAeyB,sBAAsB,CACzC,mBACA;QAEF,MAAMzB,eAAe6B,cAAc,CAAC;YAClC3B;YACAC,oBAAoBuF;YACpB3F,aAAa4E;QACf;QAEAjF,IAAIiG,YAAY,CAACC,6BAA6B,GAAG;QACjD,MAAMlG,IAAIU,KAAK,CAACyF,oBAAoB,CAClC,iCACAnG,IAAIiG,YAAY,CAACC,6BAA6B;IAElD,OAAO;QACLlG,IAAIiG,YAAY,CAACC,6BAA6B,GAAGF;QACjD,MAAMhG,IAAIU,KAAK,CAACyF,oBAAoB,CAClC,iCACAnG,IAAIiG,YAAY,CAACC,6BAA6B;IAElD;IAEA,IAAIrB,YAAY;QACd,MAAMvD,MAAMJ,IAAAA,qBAAW,EAAC,QAAQ,UAAU;QAE1C,MAAM8B,WAAW6B,WAAW7B,QAAQ;QAEpC,eAAeoD;gBAMX9F;YALF,MAAMiB,kBAAkB,MAAMyB,SAASxB,WAAW;YAClDxB,IAAIU,KAAK,CAACe,qBAAqB,CAACH,KAAKC;YACrCG,IAAAA,oBAAa,EAACtB,oBAAoBkB,KAAKC,iBAAiB,OAAOZ;YAC/D,MAAML,eAAeyB,sBAAsB,CAAC,cAAc;YAC1D,MAAMsE,oBACJ/F,wCAAAA,eAAegG,qBAAqB,CAAChF,yBAArChB,sCAA2CuE,UAAU,CAAC,IAAI;YAE5D,IAAI7E,OAAOqG,kBAAkB;gBAC3BrG,IAAIiG,YAAY,CAACpB,UAAU,GAAG;oBAC5B0B,OAAO;oBACPtG,MAAM;oBACNuG,UAAUH,iBAAiBG,QAAQ;gBACrC;YACF;QACF;QACA,MAAMJ;QAEN,IAAIpG,KAAK;YACPA,uBAAAA,IAAKU,KAAK,CAAC0B,kBAAkB,CAC3Bd,KACA,OACA0B,UACA;gBACE,MAAMyD,iBAAiBzG,IAAIU,KAAK,CAACgG,aAAa,CAC5C,cACAV,WACA;gBAEF,MAAMI;gBACN,MAAMpG,IAAIU,KAAK,CAACyF,oBAAoB,CAClC,wBACAnG,IAAIiG,YAAY,CAACU,oBAAoB;gBAEvC,MAAM3G,IAAIU,KAAK,CAACyF,oBAAoB,CAClC,cACAnG,IAAIiG,YAAY,CAACpB,UAAU;gBAE7B,MAAMvE,eAAe6B,cAAc,CAAC;oBAClC3B;oBACAC,oBAAoBuF;oBACpB3F,aAAa4E;gBACf;gBAEAwB,kCAAAA;gBACA,OAAO;oBAAElE,OAAOC,6CAA2B,CAACoD,kBAAkB;gBAAC;YACjE,GACA;gBACE,OAAO;oBACLrD,OAAOC,6CAA2B,CAACoD,kBAAkB;gBACvD;YACF;QAEJ;IACF,OAAO;QACLtF,eAAe0B,wBAAwB,CACrCd,IAAAA,qBAAW,EAAC,QAAQ,UAAU;QAEhClB,IAAIiG,YAAY,CAACU,oBAAoB,GAAGX;QACxChG,IAAIiG,YAAY,CAACpB,UAAU,GAAGmB;IAChC;IAEA,MAAMhG,IAAIU,KAAK,CAACyF,oBAAoB,CAClC,wBACAnG,IAAIiG,YAAY,CAACU,oBAAoB;IAEvC,MAAM3G,IAAIU,KAAK,CAACyF,oBAAoB,CAClC,cACAnG,IAAIiG,YAAY,CAACpB,UAAU;AAE/B;AAEA,eAAeY,4BAA4B,EACzCrF,kBAAkB,EAClB6E,kBAAkB,EAElBP,WAAW,EACXkC,mBAAmB,EACnBC,OAAO,EACPC,YAAY,EAEZpG,KAAK,EAIqB;IAC1B,yEAAyE;IACzE,KAAK,MAAMY,OAAOoD,YAAYF,IAAI,GAAI;QACpC,IAAI,CAAC1G,oBAAoBmH,oBAAoB3D,KAAKoD,cAAc;YAC9DA,YAAYpC,MAAM,CAAChB;QACrB;IACF;IAEA,KAAK,MAAMA,OAAOsF,oBAAoBpC,IAAI,GAAI;QAC5C,mCAAmC;QACnC,IAAI,CAAC1G,oBAAoBmH,oBAAoB3D,KAAKoD,cAAc;YAC9D,MAAMhE,MAAMgF,sBAAsB,CAACpE;QACrC;IACF;IAEA,KAAK,MAAM,CAACA,IAAI,IAAIlB,mBAAoB;QACtC,IAAI,CAACtC,oBAAoBmH,oBAAoB3D,KAAKoD,cAAc;YAC9DtE,mBAAmBkC,MAAM,CAAChB;QAC5B;IACF;IAEA,KAAK,MAAMyF,UAAUF,QAAS;QAC5B,MAAMG,QAAQF,aAAa7C,GAAG,CAAC8C;QAC/B,IAAI,CAACC,OAAO;YACV;QACF;QAEA,KAAK,MAAM1F,OAAO0F,MAAMC,YAAY,CAACzC,IAAI,GAAI;YAC3C,IAAI,CAAC1G,oBAAoBmH,oBAAoB3D,KAAKoD,cAAc;gBAC9DsC,MAAMC,YAAY,CAAC3E,MAAM,CAAChB;YAC5B;QACF;QAEA,KAAK,MAAM4F,MAAMF,MAAMG,aAAa,CAAC3C,IAAI,GAAI;YAC3C,IACE,CAAC1G,oBACCmH,oBACA/D,IAAAA,qBAAW,EAAC,UAAU,UAAUgG,KAChCxC,cAEF;gBACAhE,MAAM0G,wBAAwB,CAACL,QAAQG;YACzC;QACF;IACF;AACF;AAEO,eAAetJ,sBAAsB,EAC1CwC,kBAAkB,EAClBC,WAAW,EACXC,cAAc,EACdE,WAAW,EACXC,kBAAkB,EAClBE,SAAS,EACTD,KAAK,EASN;IACC,IAAIL,YAAYe,MAAM,CAACC,GAAG,EAAE;QAC1B,MAAMC,MAAMJ,IAAAA,qBAAW,EAAC,SAAS,UAAU;QAE3C,MAAMK,kBAAkB,MAAMlB,YAAYe,MAAM,CAACC,GAAG,CAACG,WAAW;QAChEd,MAAMe,qBAAqB,CAACH,KAAKC;QACjCb,MAAM0B,kBAAkB,CACtBd,KACA,OACAjB,YAAYe,MAAM,CAACC,GAAG,EACtB;YACE,oEAAoE;YACpE,qIAAqI;YACrI,OAAO;gBAAEkB,OAAOC,6CAA2B,CAACO,cAAc;YAAC;QAC7D,GACA;YACE,OAAO;gBACLH,QAAQJ,6CAA2B,CAACK,WAAW;gBAC/CC,MAAM;YACR;QACF;QAEFpB,IAAAA,oBAAa,EAACtB,oBAAoBkB,KAAKC,iBAAiB,OAAOZ;IACjE;IACA,MAAML,eAAeqB,iBAAiB,CAAC;IACvC,MAAMrB,eAAesB,iBAAiB,CAAC;IACvC,MAAMtB,eAAe2B,gBAAgB,CAAC;IAEtC,IAAI5B,YAAYe,MAAM,CAACS,QAAQ,EAAE;QAC/B,MAAMP,MAAMJ,IAAAA,qBAAW,EAAC,SAAS,UAAU;QAE3C,MAAMK,kBAAkB,MAAMlB,YAAYe,MAAM,CAACS,QAAQ,CAACL,WAAW;QACrEd,MAAMe,qBAAqB,CAACH,KAAKC;QACjCb,MAAM0B,kBAAkB,CACtBd,KACA,OACAjB,YAAYe,MAAM,CAACS,QAAQ,EAC3B;YACE,OAAO;gBACLe,QAAQJ,6CAA2B,CAACK,WAAW;gBAC/CC,MAAM;YACR;QACF,GACA,CAACH;YACC,OAAO;gBACLC,QAAQJ,6CAA2B,CAACK,WAAW;gBAC/CC,MAAM,CAAC,+CAA+C,EAAEH,GAAG;YAC7D;QACF;QAEFjB,IAAAA,oBAAa,EAACtB,oBAAoBkB,KAAKC,iBAAiB,OAAOZ;IACjE;IACA,MAAML,eAAesB,iBAAiB,CAAC;IAEvC,IAAIvB,YAAYe,MAAM,CAACwD,KAAK,EAAE;QAC5B,MAAMtD,MAAMJ,IAAAA,qBAAW,EAAC,SAAS,UAAU;QAE3C,MAAMK,kBAAkB,MAAMlB,YAAYe,MAAM,CAACwD,KAAK,CAACpD,WAAW;QAClEd,MAAMe,qBAAqB,CAACH,KAAKC;QACjCb,MAAM0B,kBAAkB,CACtBd,KACA,OACAjB,YAAYe,MAAM,CAACwD,KAAK,EACxB;YACE,oEAAoE;YACpE,qIAAqI;YACrI,OAAO;gBAAErC,OAAOC,6CAA2B,CAACO,cAAc;YAAC;QAC7D,GACA,CAACJ;YACC,OAAO;gBACLC,QAAQJ,6CAA2B,CAACK,WAAW;gBAC/CC,MAAM,CAAC,8BAA8B,EAAEH,GAAG;YAC5C;QACF;QAEFjB,IAAAA,oBAAa,EAACtB,oBAAoBkB,KAAKC,iBAAiB,OAAOZ;IACjE;IACA,MAAML,eAAeqB,iBAAiB,CAAC;IACvC,MAAMrB,eAAesB,iBAAiB,CAAC;IACvC,MAAMtB,eAAe2B,gBAAgB,CAAC;IAEtC,MAAM3B,eAAe6B,cAAc,CAAC;QAClC3B;QACAC;QACAJ;IACF;AACF;AAEO,SAASlC,kBAAkBgC,KAAa;IAC7C,OAAOA,MAAMkH,OAAO,CAAC,YAAY;AACnC;AAEO,SAAS3J,eAAeyC,KAAa;IAC1C,OAAOA,QAAQ;AACjB;AAEO,SAAS1C,qBAAqB0C,KAAa;IAChD,OAAOA,QAAQ;AACjB;AAMO,SAASnC,wCACdmC,KAAa,EACbmH,GAAmB;IAEnB,IAAIC,gBAAgBpH;IACpB,IAAIqH,IAAAA,gCAAe,EAACD,gBAAgB;QAClCA,gBAAgBA,cAAcE,QAAQ,CAAC,YACnCF,cAAcG,KAAK,CAAC,GAAG,CAAC,SAASC,MAAM,IACvCJ;QAEJ,IAAID,KAAK;YACP,IAAIC,cAAcE,QAAQ,CAAC,uBAAuB;gBAChDF,gBAAgBA,cAAcG,KAAK,CAAC,GAAG,CAAC,qBAAqBC,MAAM;YACrE;YACA,IAAIJ,cAAcE,QAAQ,CAAC,mBAAmBH,QAAQ,QAAQ;gBAC5D,kDAAkD;gBAClDC,gBAAgBA,cAAcG,KAAK,CAAC,GAAG,CAAC,OAAOC,MAAM;YACvD;QACF;QACAJ,gBAAgBA,gBAAgB;IAClC;IACA,OAAOA;AACT"}