{"version": 3, "sources": ["../../../../src/client/components/errors/use-error-handler.ts"], "sourcesContent": ["import { useEffect } from 'react'\nimport { attachHydrationErrorState } from './attach-hydration-error-state'\nimport { isNextRouterError } from '../is-next-router-error'\nimport { storeHydrationErrorStateFromConsoleArgs } from './hydration-error-info'\nimport { formatConsoleArgs, parseConsoleArgs } from '../../lib/console'\nimport isError from '../../../lib/is-error'\nimport { createUnhandledError } from './console-error'\nimport { enqueueConsecutiveDedupedError } from './enqueue-client-error'\nimport { getReactStitchedError } from '../errors/stitched-error'\n\nconst queueMicroTask =\n  globalThis.queueMicrotask || ((cb: () => void) => Promise.resolve().then(cb))\n\nexport type ErrorHandler = (error: Error) => void\n\nconst errorQueue: Array<Error> = []\nconst errorHandlers: Array<ErrorHandler> = []\nconst rejectionQueue: Array<Error> = []\nconst rejectionHandlers: Array<ErrorHandler> = []\n\nexport function handleClientError(\n  originError: unknown,\n  consoleErrorArgs: any[],\n  capturedFromConsole: boolean = false\n) {\n  let error: Error\n  if (!originError || !isError(originError)) {\n    // If it's not an error, format the args into an error\n    const formattedErrorMessage = formatConsoleArgs(consoleErrorArgs)\n    const { environmentName } = parseConsoleArgs(consoleErrorArgs)\n    error = createUnhandledError(formattedErrorMessage, environmentName)\n  } else {\n    error = capturedFromConsole\n      ? createUnhandledError(originError)\n      : originError\n  }\n  error = getReactStitchedError(error)\n\n  storeHydrationErrorStateFromConsoleArgs(...consoleErrorArgs)\n  attachHydrationErrorState(error)\n\n  enqueueConsecutiveDedupedError(errorQueue, error)\n  for (const handler of errorHandlers) {\n    // Delayed the error being passed to React Dev Overlay,\n    // avoid the state being synchronously updated in the component.\n    queueMicroTask(() => {\n      handler(error)\n    })\n  }\n}\n\nexport function useErrorHandler(\n  handleOnUnhandledError: ErrorHandler,\n  handleOnUnhandledRejection: ErrorHandler\n) {\n  useEffect(() => {\n    // Handle queued errors.\n    errorQueue.forEach(handleOnUnhandledError)\n    rejectionQueue.forEach(handleOnUnhandledRejection)\n\n    // Listen to new errors.\n    errorHandlers.push(handleOnUnhandledError)\n    rejectionHandlers.push(handleOnUnhandledRejection)\n\n    return () => {\n      // Remove listeners.\n      errorHandlers.splice(errorHandlers.indexOf(handleOnUnhandledError), 1)\n      rejectionHandlers.splice(\n        rejectionHandlers.indexOf(handleOnUnhandledRejection),\n        1\n      )\n\n      // Reset error queues.\n      errorQueue.splice(0, errorQueue.length)\n      rejectionQueue.splice(0, rejectionQueue.length)\n    }\n  }, [handleOnUnhandledError, handleOnUnhandledRejection])\n}\n\nfunction onUnhandledError(event: WindowEventMap['error']): void | boolean {\n  if (isNextRouterError(event.error)) {\n    event.preventDefault()\n    return false\n  }\n  // When there's an error property present, we log the error to error overlay.\n  // Otherwise we don't do anything as it's not logging in the console either.\n  if (event.error) {\n    handleClientError(event.error, [])\n  }\n}\n\nfunction onUnhandledRejection(ev: WindowEventMap['unhandledrejection']): void {\n  const reason = ev?.reason\n  if (isNextRouterError(reason)) {\n    ev.preventDefault()\n    return\n  }\n\n  let error = reason\n  if (error && !isError(error)) {\n    error = createUnhandledError(error + '')\n  }\n\n  rejectionQueue.push(error)\n  for (const handler of rejectionHandlers) {\n    handler(error)\n  }\n}\n\nexport function handleGlobalErrors() {\n  if (typeof window !== 'undefined') {\n    try {\n      // Increase the number of stack frames on the client\n      Error.stackTraceLimit = 50\n    } catch {}\n\n    window.addEventListener('error', onUnhandledError)\n    window.addEventListener('unhandledrejection', onUnhandledRejection)\n  }\n}\n"], "names": ["useEffect", "attachHydrationErrorState", "isNextRouterError", "storeHydrationErrorStateFromConsoleArgs", "formatConsoleArgs", "parseConsoleArgs", "isError", "createUnhandledError", "enqueueConsecutiveDedupedError", "getReactStitchedError", "queueMicroTask", "globalThis", "queueMicrotask", "cb", "Promise", "resolve", "then", "errorQueue", "errorHandlers", "rejectionQueue", "rejectionHandlers", "handleClientError", "originError", "consoleErrorArgs", "capturedFromConsole", "error", "formattedErrorMessage", "environmentName", "handler", "useErrorHandler", "handleOnUnhandledError", "handleOnUnhandledRejection", "for<PERSON>ach", "push", "splice", "indexOf", "length", "onUnhandledError", "event", "preventDefault", "onUnhandledRejection", "ev", "reason", "handleGlobalErrors", "window", "Error", "stackTraceLimit", "addEventListener"], "mappings": "AAAA,SAASA,SAAS,QAAQ,QAAO;AACjC,SAASC,yBAAyB,QAAQ,iCAAgC;AAC1E,SAASC,iBAAiB,QAAQ,0BAAyB;AAC3D,SAASC,uCAAuC,QAAQ,yBAAwB;AAChF,SAASC,iBAAiB,EAAEC,gBAAgB,QAAQ,oBAAmB;AACvE,OAAOC,aAAa,wBAAuB;AAC3C,SAASC,oBAAoB,QAAQ,kBAAiB;AACtD,SAASC,8BAA8B,QAAQ,yBAAwB;AACvE,SAASC,qBAAqB,QAAQ,2BAA0B;AAEhE,MAAMC,iBACJC,WAAWC,cAAc,IAAK,CAAA,CAACC,KAAmBC,QAAQC,OAAO,GAAGC,IAAI,CAACH,GAAE;AAI7E,MAAMI,aAA2B,EAAE;AACnC,MAAMC,gBAAqC,EAAE;AAC7C,MAAMC,iBAA+B,EAAE;AACvC,MAAMC,oBAAyC,EAAE;AAEjD,OAAO,SAASC,kBACdC,WAAoB,EACpBC,gBAAuB,EACvBC,mBAAoC;IAApCA,IAAAA,gCAAAA,sBAA+B;IAE/B,IAAIC;IACJ,IAAI,CAACH,eAAe,CAAChB,QAAQgB,cAAc;QACzC,sDAAsD;QACtD,MAAMI,wBAAwBtB,kBAAkBmB;QAChD,MAAM,EAAEI,eAAe,EAAE,GAAGtB,iBAAiBkB;QAC7CE,QAAQlB,qBAAqBmB,uBAAuBC;IACtD,OAAO;QACLF,QAAQD,sBACJjB,qBAAqBe,eACrBA;IACN;IACAG,QAAQhB,sBAAsBgB;IAE9BtB,2CAA2CoB;IAC3CtB,0BAA0BwB;IAE1BjB,+BAA+BS,YAAYQ;IAC3C,KAAK,MAAMG,WAAWV,cAAe;QACnC,uDAAuD;QACvD,gEAAgE;QAChER,eAAe;YACbkB,QAAQH;QACV;IACF;AACF;AAEA,OAAO,SAASI,gBACdC,sBAAoC,EACpCC,0BAAwC;IAExC/B,UAAU;QACR,wBAAwB;QACxBiB,WAAWe,OAAO,CAACF;QACnBX,eAAea,OAAO,CAACD;QAEvB,wBAAwB;QACxBb,cAAce,IAAI,CAACH;QACnBV,kBAAkBa,IAAI,CAACF;QAEvB,OAAO;YACL,oBAAoB;YACpBb,cAAcgB,MAAM,CAAChB,cAAciB,OAAO,CAACL,yBAAyB;YACpEV,kBAAkBc,MAAM,CACtBd,kBAAkBe,OAAO,CAACJ,6BAC1B;YAGF,sBAAsB;YACtBd,WAAWiB,MAAM,CAAC,GAAGjB,WAAWmB,MAAM;YACtCjB,eAAee,MAAM,CAAC,GAAGf,eAAeiB,MAAM;QAChD;IACF,GAAG;QAACN;QAAwBC;KAA2B;AACzD;AAEA,SAASM,iBAAiBC,KAA8B;IACtD,IAAIpC,kBAAkBoC,MAAMb,KAAK,GAAG;QAClCa,MAAMC,cAAc;QACpB,OAAO;IACT;IACA,6EAA6E;IAC7E,4EAA4E;IAC5E,IAAID,MAAMb,KAAK,EAAE;QACfJ,kBAAkBiB,MAAMb,KAAK,EAAE,EAAE;IACnC;AACF;AAEA,SAASe,qBAAqBC,EAAwC;IACpE,MAAMC,SAASD,sBAAAA,GAAIC,MAAM;IACzB,IAAIxC,kBAAkBwC,SAAS;QAC7BD,GAAGF,cAAc;QACjB;IACF;IAEA,IAAId,QAAQiB;IACZ,IAAIjB,SAAS,CAACnB,QAAQmB,QAAQ;QAC5BA,QAAQlB,qBAAqBkB,QAAQ;IACvC;IAEAN,eAAec,IAAI,CAACR;IACpB,KAAK,MAAMG,WAAWR,kBAAmB;QACvCQ,QAAQH;IACV;AACF;AAEA,OAAO,SAASkB;IACd,IAAI,OAAOC,WAAW,aAAa;QACjC,IAAI;YACF,oDAAoD;YACpDC,MAAMC,eAAe,GAAG;QAC1B,EAAE,UAAM,CAAC;QAETF,OAAOG,gBAAgB,CAAC,SAASV;QACjCO,OAAOG,gBAAgB,CAAC,sBAAsBP;IAChD;AACF"}