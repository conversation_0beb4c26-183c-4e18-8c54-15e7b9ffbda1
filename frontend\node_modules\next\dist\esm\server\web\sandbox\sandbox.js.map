{"version": 3, "sources": ["../../../../src/server/web/sandbox/sandbox.ts"], "sourcesContent": ["import type { NodejsRequestData, FetchEventResult, RequestData } from '../types'\nimport type { EdgeFunctionDefinition } from '../../../build/webpack/plugins/middleware-plugin'\nimport type { EdgeRuntime } from 'next/dist/compiled/edge-runtime'\nimport {\n  getModuleContext,\n  requestStore,\n  edgeSandboxNextRequestContext,\n} from './context'\nimport { requestToBodyStream } from '../../body-streams'\nimport { NEXT_RSC_UNION_QUERY } from '../../../client/components/app-router-headers'\nimport type { ServerComponentsHmrCache } from '../../response-cache'\nimport {\n  getBuiltinRequestContext,\n  type BuiltinRequestContextValue,\n} from '../../after/builtin-request-context'\n\nexport const ErrorSource = Symbol('SandboxError')\n\nconst FORBIDDEN_HEADERS = [\n  'content-length',\n  'content-encoding',\n  'transfer-encoding',\n]\n\ninterface RunnerFnParams {\n  name: string\n  onError?: (err: unknown) => void\n  onWarning?: (warn: Error) => void\n  paths: string[]\n  request: NodejsRequestData\n  useCache: boolean\n  edgeFunctionEntry: Pick<EdgeFunctionDefinition, 'assets' | 'wasm' | 'env'>\n  distDir: string\n  incrementalCache?: any\n  serverComponentsHmrCache?: ServerComponentsHmrCache\n}\n\ntype RunnerFn = (params: RunnerFnParams) => Promise<FetchEventResult>\n\n/**\n * Decorates the runner function making sure all errors it can produce are\n * tagged with `edge-server` so they can properly be rendered in dev.\n */\nfunction withTaggedErrors(fn: RunnerFn): RunnerFn {\n  if (process.env.NODE_ENV === 'development') {\n    const { getServerError } =\n      require('../../../client/components/react-dev-overlay/server/middleware-webpack') as typeof import('../../../client/components/react-dev-overlay/server/middleware-webpack')\n\n    return (params) =>\n      fn(params)\n        .then((result) => ({\n          ...result,\n          waitUntil: result?.waitUntil?.catch((error) => {\n            // TODO: used COMPILER_NAMES.edgeServer instead. Verify that it does not increase the runtime size.\n            throw getServerError(error, 'edge-server')\n          }),\n        }))\n        .catch((error) => {\n          // TODO: used COMPILER_NAMES.edgeServer instead\n          throw getServerError(error, 'edge-server')\n        })\n  }\n\n  return fn\n}\n\nexport async function getRuntimeContext(\n  params: Omit<RunnerFnParams, 'request'>\n): Promise<EdgeRuntime<any>> {\n  const { runtime, evaluateInContext } = await getModuleContext({\n    moduleName: params.name,\n    onWarning: params.onWarning ?? (() => {}),\n    onError: params.onError ?? (() => {}),\n    useCache: params.useCache !== false,\n    edgeFunctionEntry: params.edgeFunctionEntry,\n    distDir: params.distDir,\n  })\n\n  if (params.incrementalCache) {\n    runtime.context.globalThis.__incrementalCache = params.incrementalCache\n  }\n\n  if (params.serverComponentsHmrCache) {\n    runtime.context.globalThis.__serverComponentsHmrCache =\n      params.serverComponentsHmrCache\n  }\n\n  for (const paramPath of params.paths) {\n    evaluateInContext(paramPath)\n  }\n  return runtime\n}\n\nexport const run = withTaggedErrors(async function runWithTaggedErrors(params) {\n  const runtime = await getRuntimeContext(params)\n\n  const edgeFunction: (args: {\n    request: RequestData\n  }) => Promise<FetchEventResult> = (\n    await runtime.context._ENTRIES[`middleware_${params.name}`]\n  ).default\n\n  const cloned = !['HEAD', 'GET'].includes(params.request.method)\n    ? params.request.body?.cloneBodyStream()\n    : undefined\n\n  const KUint8Array = runtime.evaluate('Uint8Array')\n  const urlInstance = new URL(params.request.url)\n  urlInstance.searchParams.delete(NEXT_RSC_UNION_QUERY)\n\n  params.request.url = urlInstance.toString()\n\n  const headers = new Headers()\n  for (const [key, value] of Object.entries(params.request.headers)) {\n    headers.set(key, value?.toString() ?? '')\n  }\n\n  try {\n    let result: FetchEventResult | undefined = undefined\n    const builtinRequestCtx: BuiltinRequestContextValue = {\n      ...getBuiltinRequestContext(),\n      // FIXME(after):\n      // arguably, this is an abuse of \"@next/request-context\" --\n      // it'd make more sense to simply forward its existing value into the sandbox (in `createModuleContext`)\n      // but here we're using it to just pass in `waitUntil` regardless if we were running in this context or not.\n      waitUntil: params.request.waitUntil,\n    }\n    await edgeSandboxNextRequestContext.run(builtinRequestCtx, () =>\n      requestStore.run({ headers }, async () => {\n        result = await edgeFunction({\n          request: {\n            ...params.request,\n            body:\n              cloned &&\n              requestToBodyStream(runtime.context, KUint8Array, cloned),\n          },\n        })\n        for (const headerName of FORBIDDEN_HEADERS) {\n          result.response.headers.delete(headerName)\n        }\n      })\n    )\n\n    if (!result) throw new Error('Edge function did not return a response')\n    return result\n  } finally {\n    await params.request.body?.finalize()\n  }\n})\n"], "names": ["getModuleContext", "requestStore", "edgeSandboxNextRequestContext", "requestToBodyStream", "NEXT_RSC_UNION_QUERY", "getBuiltinRequestContext", "ErrorSource", "Symbol", "FORBIDDEN_HEADERS", "withTaggedErrors", "fn", "process", "env", "NODE_ENV", "getServerError", "require", "params", "then", "result", "waitUntil", "catch", "error", "getRuntimeContext", "runtime", "evaluateInContext", "moduleName", "name", "onWarning", "onError", "useCache", "edgeFunctionEntry", "distDir", "incrementalCache", "context", "globalThis", "__incrementalCache", "serverComponentsHmrCache", "__serverComponentsHmrCache", "<PERSON><PERSON><PERSON><PERSON>", "paths", "run", "runWithTaggedErrors", "edgeFunction", "_ENTRIES", "default", "cloned", "includes", "request", "method", "body", "cloneBodyStream", "undefined", "KUint8Array", "evaluate", "urlInstance", "URL", "url", "searchParams", "delete", "toString", "headers", "Headers", "key", "value", "Object", "entries", "set", "builtinRequestCtx", "headerName", "response", "Error", "finalize"], "mappings": "AAGA,SACEA,gBAAgB,EAChBC,YAAY,EACZC,6BAA6B,QACxB,YAAW;AAClB,SAASC,mBAAmB,QAAQ,qBAAoB;AACxD,SAASC,oBAAoB,QAAQ,gDAA+C;AAEpF,SACEC,wBAAwB,QAEnB,sCAAqC;AAE5C,OAAO,MAAMC,cAAcC,OAAO,gBAAe;AAEjD,MAAMC,oBAAoB;IACxB;IACA;IACA;CACD;AAiBD;;;CAGC,GACD,SAASC,iBAAiBC,EAAY;IACpC,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;QAC1C,MAAM,EAAEC,cAAc,EAAE,GACtBC,QAAQ;QAEV,OAAO,CAACC,SACNN,GAAGM,QACAC,IAAI,CAAC,CAACC;oBAEMA;uBAFM;oBACjB,GAAGA,MAAM;oBACTC,SAAS,EAAED,2BAAAA,oBAAAA,OAAQC,SAAS,qBAAjBD,kBAAmBE,KAAK,CAAC,CAACC;wBACnC,mGAAmG;wBACnG,MAAMP,eAAeO,OAAO;oBAC9B;gBACF;eACCD,KAAK,CAAC,CAACC;gBACN,+CAA+C;gBAC/C,MAAMP,eAAeO,OAAO;YAC9B;IACN;IAEA,OAAOX;AACT;AAEA,OAAO,eAAeY,kBACpBN,MAAuC;IAEvC,MAAM,EAAEO,OAAO,EAAEC,iBAAiB,EAAE,GAAG,MAAMxB,iBAAiB;QAC5DyB,YAAYT,OAAOU,IAAI;QACvBC,WAAWX,OAAOW,SAAS,IAAK,CAAA,KAAO,CAAA;QACvCC,SAASZ,OAAOY,OAAO,IAAK,CAAA,KAAO,CAAA;QACnCC,UAAUb,OAAOa,QAAQ,KAAK;QAC9BC,mBAAmBd,OAAOc,iBAAiB;QAC3CC,SAASf,OAAOe,OAAO;IACzB;IAEA,IAAIf,OAAOgB,gBAAgB,EAAE;QAC3BT,QAAQU,OAAO,CAACC,UAAU,CAACC,kBAAkB,GAAGnB,OAAOgB,gBAAgB;IACzE;IAEA,IAAIhB,OAAOoB,wBAAwB,EAAE;QACnCb,QAAQU,OAAO,CAACC,UAAU,CAACG,0BAA0B,GACnDrB,OAAOoB,wBAAwB;IACnC;IAEA,KAAK,MAAME,aAAatB,OAAOuB,KAAK,CAAE;QACpCf,kBAAkBc;IACpB;IACA,OAAOf;AACT;AAEA,OAAO,MAAMiB,MAAM/B,iBAAiB,eAAegC,oBAAoBzB,MAAM;QAUvEA;IATJ,MAAMO,UAAU,MAAMD,kBAAkBN;IAExC,MAAM0B,eAE4B,AAChC,CAAA,MAAMnB,QAAQU,OAAO,CAACU,QAAQ,CAAC,CAAC,WAAW,EAAE3B,OAAOU,IAAI,EAAE,CAAC,AAAD,EAC1DkB,OAAO;IAET,MAAMC,SAAS,CAAC;QAAC;QAAQ;KAAM,CAACC,QAAQ,CAAC9B,OAAO+B,OAAO,CAACC,MAAM,KAC1DhC,uBAAAA,OAAO+B,OAAO,CAACE,IAAI,qBAAnBjC,qBAAqBkC,eAAe,KACpCC;IAEJ,MAAMC,cAAc7B,QAAQ8B,QAAQ,CAAC;IACrC,MAAMC,cAAc,IAAIC,IAAIvC,OAAO+B,OAAO,CAACS,GAAG;IAC9CF,YAAYG,YAAY,CAACC,MAAM,CAACtD;IAEhCY,OAAO+B,OAAO,CAACS,GAAG,GAAGF,YAAYK,QAAQ;IAEzC,MAAMC,UAAU,IAAIC;IACpB,KAAK,MAAM,CAACC,KAAKC,MAAM,IAAIC,OAAOC,OAAO,CAACjD,OAAO+B,OAAO,CAACa,OAAO,EAAG;QACjEA,QAAQM,GAAG,CAACJ,KAAKC,CAAAA,yBAAAA,MAAOJ,QAAQ,OAAM;IACxC;IAEA,IAAI;QACF,IAAIzC,SAAuCiC;QAC3C,MAAMgB,oBAAgD;YACpD,GAAG9D,0BAA0B;YAC7B,gBAAgB;YAChB,2DAA2D;YAC3D,wGAAwG;YACxG,4GAA4G;YAC5Gc,WAAWH,OAAO+B,OAAO,CAAC5B,SAAS;QACrC;QACA,MAAMjB,8BAA8BsC,GAAG,CAAC2B,mBAAmB,IACzDlE,aAAauC,GAAG,CAAC;gBAAEoB;YAAQ,GAAG;gBAC5B1C,SAAS,MAAMwB,aAAa;oBAC1BK,SAAS;wBACP,GAAG/B,OAAO+B,OAAO;wBACjBE,MACEJ,UACA1C,oBAAoBoB,QAAQU,OAAO,EAAEmB,aAAaP;oBACtD;gBACF;gBACA,KAAK,MAAMuB,cAAc5D,kBAAmB;oBAC1CU,OAAOmD,QAAQ,CAACT,OAAO,CAACF,MAAM,CAACU;gBACjC;YACF;QAGF,IAAI,CAAClD,QAAQ,MAAM,qBAAoD,CAApD,IAAIoD,MAAM,4CAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAmD;QACtE,OAAOpD;IACT,SAAU;YACFF;QAAN,QAAMA,wBAAAA,OAAO+B,OAAO,CAACE,IAAI,qBAAnBjC,sBAAqBuD,QAAQ;IACrC;AACF,GAAE"}