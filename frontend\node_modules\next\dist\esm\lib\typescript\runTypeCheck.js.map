{"version": 3, "sources": ["../../../src/lib/typescript/runTypeCheck.ts"], "sourcesContent": ["import path from 'path'\nimport {\n  DiagnosticCategory,\n  getFormattedDiagnostic,\n} from './diagnosticFormatter'\nimport { getTypeScriptConfiguration } from './getTypeScriptConfiguration'\nimport { getRequiredConfiguration } from './writeConfigurationDefaults'\n\nimport { CompileError } from '../compile-error'\nimport { warn } from '../../build/output/log'\n\nexport interface TypeCheckResult {\n  hasWarnings: boolean\n  warnings?: string[]\n  inputFilesCount: number\n  totalFilesCount: number\n  incremental: boolean\n}\n\nexport async function runTypeCheck(\n  ts: typeof import('typescript'),\n  baseDir: string,\n  distDir: string,\n  tsConfigPath: string,\n  cacheDir?: string,\n  isAppDirEnabled?: boolean\n): Promise<TypeCheckResult> {\n  const effectiveConfiguration = await getTypeScriptConfiguration(\n    ts,\n    tsConfigPath\n  )\n\n  if (effectiveConfiguration.fileNames.length < 1) {\n    return {\n      hasWarnings: false,\n      inputFilesCount: 0,\n      totalFilesCount: 0,\n      incremental: false,\n    }\n  }\n  const requiredConfig = getRequiredConfiguration(ts)\n\n  const options = {\n    ...requiredConfig,\n    ...effectiveConfiguration.options,\n    declarationMap: false,\n    emitDeclarationOnly: false,\n    noEmit: true,\n  }\n\n  let program:\n    | import('typescript').Program\n    | import('typescript').BuilderProgram\n  let incremental = false\n  if ((options.incremental || options.composite) && cacheDir) {\n    if (options.composite) {\n      warn(\n        'TypeScript project references are not fully supported. Attempting to build in incremental mode.'\n      )\n    }\n    incremental = true\n    program = ts.createIncrementalProgram({\n      rootNames: effectiveConfiguration.fileNames,\n      options: {\n        ...options,\n        composite: false,\n        incremental: true,\n        tsBuildInfoFile: path.join(cacheDir, '.tsbuildinfo'),\n      },\n    })\n  } else {\n    program = ts.createProgram(effectiveConfiguration.fileNames, options)\n  }\n\n  const result = program.emit()\n\n  const ignoreRegex = [\n    // matches **/__(tests|mocks)__/**\n    /[\\\\/]__(?:tests|mocks)__[\\\\/]/,\n    // matches **/*.(spec|test).*\n    /(?<=[\\\\/.])(?:spec|test)\\.[^\\\\/]+$/,\n  ]\n  const regexIgnoredFile = new RegExp(\n    ignoreRegex.map((r) => r.source).join('|')\n  )\n\n  const allDiagnostics = ts\n    .getPreEmitDiagnostics(program as import('typescript').Program)\n    .concat(result.diagnostics)\n    .filter((d) => !(d.file && regexIgnoredFile.test(d.file.fileName)))\n\n  const firstError =\n    allDiagnostics.find(\n      (d) => d.category === DiagnosticCategory.Error && Boolean(d.file)\n    ) ?? allDiagnostics.find((d) => d.category === DiagnosticCategory.Error)\n\n  // In test mode, we want to check all diagnostics, not just the first one.\n  if (process.env.__NEXT_TEST_MODE) {\n    if (firstError) {\n      const allErrors = allDiagnostics\n        .filter((d) => d.category === DiagnosticCategory.Error)\n        .map(\n          (d) =>\n            '[Test Mode] ' +\n            getFormattedDiagnostic(ts, baseDir, distDir, d, isAppDirEnabled)\n        )\n\n      console.error(\n        '\\n\\n===== TS errors =====\\n\\n' +\n          allErrors.join('\\n\\n') +\n          '\\n\\n===== TS errors =====\\n\\n'\n      )\n\n      // Make sure all stdout is flushed before we exit.\n      await new Promise((resolve) => setTimeout(resolve, 100))\n    }\n  }\n\n  if (firstError) {\n    throw new CompileError(\n      getFormattedDiagnostic(ts, baseDir, distDir, firstError, isAppDirEnabled)\n    )\n  }\n\n  const warnings = allDiagnostics\n    .filter((d) => d.category === DiagnosticCategory.Warning)\n    .map((d) =>\n      getFormattedDiagnostic(ts, baseDir, distDir, d, isAppDirEnabled)\n    )\n\n  return {\n    hasWarnings: true,\n    warnings,\n    inputFilesCount: effectiveConfiguration.fileNames.length,\n    totalFilesCount: program.getSourceFiles().length,\n    incremental,\n  }\n}\n"], "names": ["path", "DiagnosticCategory", "getFormattedDiagnostic", "getTypeScriptConfiguration", "getRequiredConfiguration", "CompileError", "warn", "runTypeCheck", "ts", "baseDir", "distDir", "tsConfigPath", "cacheDir", "isAppDirEnabled", "effectiveConfiguration", "fileNames", "length", "hasWarnings", "inputFilesCount", "totalFilesCount", "incremental", "requiredConfig", "options", "declarationMap", "emitDeclarationOnly", "noEmit", "program", "composite", "createIncrementalProgram", "rootNames", "tsBuildInfoFile", "join", "createProgram", "result", "emit", "ignoreRegex", "regexIgnoredFile", "RegExp", "map", "r", "source", "allDiagnostics", "getPreEmitDiagnostics", "concat", "diagnostics", "filter", "d", "file", "test", "fileName", "firstError", "find", "category", "Error", "Boolean", "process", "env", "__NEXT_TEST_MODE", "allErrors", "console", "error", "Promise", "resolve", "setTimeout", "warnings", "Warning", "getSourceFiles"], "mappings": "AAAA,OAAOA,UAAU,OAAM;AACvB,SACEC,kBAAkB,EAClBC,sBAAsB,QACjB,wBAAuB;AAC9B,SAASC,0BAA0B,QAAQ,+BAA8B;AACzE,SAASC,wBAAwB,QAAQ,+BAA8B;AAEvE,SAASC,YAAY,QAAQ,mBAAkB;AAC/C,SAASC,IAAI,QAAQ,yBAAwB;AAU7C,OAAO,eAAeC,aACpBC,EAA+B,EAC/BC,OAAe,EACfC,OAAe,EACfC,YAAoB,EACpBC,QAAiB,EACjBC,eAAyB;IAEzB,MAAMC,yBAAyB,MAAMX,2BACnCK,IACAG;IAGF,IAAIG,uBAAuBC,SAAS,CAACC,MAAM,GAAG,GAAG;QAC/C,OAAO;YACLC,aAAa;YACbC,iBAAiB;YACjBC,iBAAiB;YACjBC,aAAa;QACf;IACF;IACA,MAAMC,iBAAiBjB,yBAAyBI;IAEhD,MAAMc,UAAU;QACd,GAAGD,cAAc;QACjB,GAAGP,uBAAuBQ,OAAO;QACjCC,gBAAgB;QAChBC,qBAAqB;QACrBC,QAAQ;IACV;IAEA,IAAIC;IAGJ,IAAIN,cAAc;IAClB,IAAI,AAACE,CAAAA,QAAQF,WAAW,IAAIE,QAAQK,SAAS,AAAD,KAAMf,UAAU;QAC1D,IAAIU,QAAQK,SAAS,EAAE;YACrBrB,KACE;QAEJ;QACAc,cAAc;QACdM,UAAUlB,GAAGoB,wBAAwB,CAAC;YACpCC,WAAWf,uBAAuBC,SAAS;YAC3CO,SAAS;gBACP,GAAGA,OAAO;gBACVK,WAAW;gBACXP,aAAa;gBACbU,iBAAiB9B,KAAK+B,IAAI,CAACnB,UAAU;YACvC;QACF;IACF,OAAO;QACLc,UAAUlB,GAAGwB,aAAa,CAAClB,uBAAuBC,SAAS,EAAEO;IAC/D;IAEA,MAAMW,SAASP,QAAQQ,IAAI;IAE3B,MAAMC,cAAc;QAClB,kCAAkC;QAClC;QACA,6BAA6B;QAC7B;KACD;IACD,MAAMC,mBAAmB,IAAIC,OAC3BF,YAAYG,GAAG,CAAC,CAACC,IAAMA,EAAEC,MAAM,EAAET,IAAI,CAAC;IAGxC,MAAMU,iBAAiBjC,GACpBkC,qBAAqB,CAAChB,SACtBiB,MAAM,CAACV,OAAOW,WAAW,EACzBC,MAAM,CAAC,CAACC,IAAM,CAAEA,CAAAA,EAAEC,IAAI,IAAIX,iBAAiBY,IAAI,CAACF,EAAEC,IAAI,CAACE,QAAQ,CAAA;IAElE,MAAMC,aACJT,eAAeU,IAAI,CACjB,CAACL,IAAMA,EAAEM,QAAQ,KAAKnD,mBAAmBoD,KAAK,IAAIC,QAAQR,EAAEC,IAAI,MAC7DN,eAAeU,IAAI,CAAC,CAACL,IAAMA,EAAEM,QAAQ,KAAKnD,mBAAmBoD,KAAK;IAEzE,0EAA0E;IAC1E,IAAIE,QAAQC,GAAG,CAACC,gBAAgB,EAAE;QAChC,IAAIP,YAAY;YACd,MAAMQ,YAAYjB,eACfI,MAAM,CAAC,CAACC,IAAMA,EAAEM,QAAQ,KAAKnD,mBAAmBoD,KAAK,EACrDf,GAAG,CACF,CAACQ,IACC,iBACA5C,uBAAuBM,IAAIC,SAASC,SAASoC,GAAGjC;YAGtD8C,QAAQC,KAAK,CACX,kCACEF,UAAU3B,IAAI,CAAC,UACf;YAGJ,kDAAkD;YAClD,MAAM,IAAI8B,QAAQ,CAACC,UAAYC,WAAWD,SAAS;QACrD;IACF;IAEA,IAAIZ,YAAY;QACd,MAAM,qBAEL,CAFK,IAAI7C,aACRH,uBAAuBM,IAAIC,SAASC,SAASwC,YAAYrC,mBADrD,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,MAAMmD,WAAWvB,eACdI,MAAM,CAAC,CAACC,IAAMA,EAAEM,QAAQ,KAAKnD,mBAAmBgE,OAAO,EACvD3B,GAAG,CAAC,CAACQ,IACJ5C,uBAAuBM,IAAIC,SAASC,SAASoC,GAAGjC;IAGpD,OAAO;QACLI,aAAa;QACb+C;QACA9C,iBAAiBJ,uBAAuBC,SAAS,CAACC,MAAM;QACxDG,iBAAiBO,QAAQwC,cAAc,GAAGlD,MAAM;QAChDI;IACF;AACF"}