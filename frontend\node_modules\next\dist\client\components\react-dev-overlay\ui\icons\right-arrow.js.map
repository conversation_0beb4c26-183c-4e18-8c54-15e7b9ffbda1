{"version": 3, "sources": ["../../../../../../src/client/components/react-dev-overlay/ui/icons/right-arrow.tsx"], "sourcesContent": ["export function RightArrow({\n  title,\n  className,\n}: {\n  title?: string\n  className?: string\n}) {\n  return (\n    <svg\n      width=\"16\"\n      height=\"16\"\n      viewBox=\"0 0 16 16\"\n      fill=\"none\"\n      xmlns=\"http://www.w3.org/2000/svg\"\n      className={className}\n      aria-label={title}\n    >\n      <path\n        fillRule=\"evenodd\"\n        clipRule=\"evenodd\"\n        d=\"M6.75011 3.93945L7.28044 4.46978L10.1037 7.29301C10.4942 7.68353 10.4942 8.3167 10.1037 8.70722L7.28044 11.5304L6.75011 12.0608L5.68945 11.0001L6.21978 10.4698L8.68945 8.00011L6.21978 5.53044L5.68945 5.00011L6.75011 3.93945Z\"\n        fill=\"currentColor\"\n      />\n    </svg>\n  )\n}\n"], "names": ["RightArrow", "title", "className", "svg", "width", "height", "viewBox", "fill", "xmlns", "aria-label", "path", "fillRule", "clipRule", "d"], "mappings": ";;;;+BAAgBA;;;eAAAA;;;;AAAT,SAASA,WAAW,KAM1B;IAN0B,IAAA,EACzBC,KAAK,EACLC,SAAS,EAIV,GAN0B;IAOzB,qBACE,qBAACC;QACCC,OAAM;QACNC,QAAO;QACPC,SAAQ;QACRC,MAAK;QACLC,OAAM;QACNN,WAAWA;QACXO,cAAYR;kBAEZ,cAAA,qBAACS;YACCC,UAAS;YACTC,UAAS;YACTC,GAAE;YACFN,MAAK;;;AAIb"}