{"version": 3, "sources": ["../../../../src/server/lib/cache-handlers/types.ts"], "sourcesContent": ["// In-memory caches are fragile and should not use stale-while-revalidate\n// semantics on the caches because it's not worth warming up an entry that's\n// likely going to get evicted before we get to use it anyway. However,\n// we also don't want to reuse a stale entry for too long so stale entries\n// should be considered expired/missing in such CacheHandlers.\n\n// This is the entry we store\nexport interface CacheEntry {\n  /**\n   * The ReadableStream can error and only have partial\n   * data so any cache handlers need to handle this case\n   * and decide to keep the partial cache around or not\n   */\n  value: ReadableStream<Uint8Array>\n\n  /**\n   * The tags configured for the entry excluding soft tags\n   */\n  tags: string[]\n\n  /**\n   * This is for the client not used to calculate cache entry expiration\n   */\n  stale: number\n\n  /**\n   * When the cache entry was created\n   */\n  timestamp: number\n\n  /**\n   * How long the entry can last (should be longer than revalidate)\n   */\n  expire: number\n\n  /**\n   * How long until the entry should revalidate\n   */\n  revalidate: number\n}\n\nexport interface CacheHandler {\n  get(cacheKey: string, softTags: string[]): Promise<undefined | CacheEntry>\n\n  set(cacheKey: string, entry: Promise<CacheEntry>): Promise<void>\n\n  // This is called when expireTags('') is called\n  // and should update tags manifest accordingly\n  expireTags(...tags: string[]): Promise<void>\n\n  // This is called when an action request sends\n  // NEXT_CACHE_REVALIDATED_TAGS_HEADER and tells\n  // us these tags are expired and the manifest\n  // should be updated this differs since in a multi\n  // instance environment you don't propagate these\n  // as they are request specific\n  receiveExpiredTags(...tags: string[]): Promise<void>\n}\n"], "names": [], "mappings": "AAAA,yEAAyE;AACzE,4EAA4E;AAC5E,uEAAuE;AACvE,0EAA0E;AAC1E,8DAA8D;AAE9D,6BAA6B"}