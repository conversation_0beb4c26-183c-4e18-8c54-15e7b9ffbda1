{"version": 3, "sources": ["../../../src/lib/typescript/getTypeScriptConfiguration.ts"], "sourcesContent": ["import { bold, cyan } from '../picocolors'\nimport os from 'os'\nimport path from 'path'\n\nimport { FatalError } from '../fatal-error'\nimport isError from '../is-error'\n\nexport async function getTypeScriptConfiguration(\n  ts: typeof import('typescript'),\n  tsConfigPath: string,\n  metaOnly?: boolean\n): Promise<import('typescript').ParsedCommandLine> {\n  try {\n    const formatDiagnosticsHost: import('typescript').FormatDiagnosticsHost = {\n      getCanonicalFileName: (fileName: string) => fileName,\n      getCurrentDirectory: ts.sys.getCurrentDirectory,\n      getNewLine: () => os.EOL,\n    }\n\n    const { config, error } = ts.readConfigFile(tsConfigPath, ts.sys.readFile)\n    if (error) {\n      throw new FatalError(ts.formatDiagnostic(error, formatDiagnosticsHost))\n    }\n\n    let configToParse: any = config\n\n    const result = ts.parseJsonConfigFileContent(\n      configToParse,\n      // When only interested in meta info,\n      // avoid enumerating all files (for performance reasons)\n      metaOnly\n        ? {\n            ...ts.sys,\n            readDirectory(_path, extensions, _excludes, _includes, _depth) {\n              return [extensions ? `file${extensions[0]}` : `file.ts`]\n            },\n          }\n        : ts.sys,\n      path.dirname(tsConfigPath)\n    )\n\n    if (result.errors) {\n      result.errors = result.errors.filter(\n        ({ code }) =>\n          // No inputs were found in config file\n          code !== 18003\n      )\n    }\n\n    if (result.errors?.length) {\n      throw new FatalError(\n        ts.formatDiagnostic(result.errors[0], formatDiagnosticsHost)\n      )\n    }\n\n    return result\n  } catch (err) {\n    if (isError(err) && err.name === 'SyntaxError') {\n      const reason = '\\n' + (err.message ?? '')\n      throw new FatalError(\n        bold(\n          'Could not parse' +\n            cyan('tsconfig.json') +\n            '.' +\n            ' Please make sure it contains syntactically correct JSON.'\n        ) + reason\n      )\n    }\n    throw err\n  }\n}\n"], "names": ["bold", "cyan", "os", "path", "FatalE<PERSON>r", "isError", "getTypeScriptConfiguration", "ts", "tsConfigPath", "metaOnly", "result", "formatDiagnosticsHost", "getCanonicalFileName", "fileName", "getCurrentDirectory", "sys", "getNewLine", "EOL", "config", "error", "readConfigFile", "readFile", "formatDiagnostic", "configToParse", "parseJsonConfigFileContent", "readDirectory", "_path", "extensions", "_excludes", "_includes", "_depth", "dirname", "errors", "filter", "code", "length", "err", "name", "reason", "message"], "mappings": "AAAA,SAASA,IAAI,EAAEC,IAAI,QAAQ,gBAAe;AAC1C,OAAOC,QAAQ,KAAI;AACnB,OAAOC,UAAU,OAAM;AAEvB,SAASC,UAAU,QAAQ,iBAAgB;AAC3C,OAAOC,aAAa,cAAa;AAEjC,OAAO,eAAeC,2BACpBC,EAA+B,EAC/BC,YAAoB,EACpBC,QAAkB;IAElB,IAAI;YAqCEC;QApCJ,MAAMC,wBAAoE;YACxEC,sBAAsB,CAACC,WAAqBA;YAC5CC,qBAAqBP,GAAGQ,GAAG,CAACD,mBAAmB;YAC/CE,YAAY,IAAMd,GAAGe,GAAG;QAC1B;QAEA,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAE,GAAGZ,GAAGa,cAAc,CAACZ,cAAcD,GAAGQ,GAAG,CAACM,QAAQ;QACzE,IAAIF,OAAO;YACT,MAAM,qBAAiE,CAAjE,IAAIf,WAAWG,GAAGe,gBAAgB,CAACH,OAAOR,yBAA1C,qBAAA;uBAAA;4BAAA;8BAAA;YAAgE;QACxE;QAEA,IAAIY,gBAAqBL;QAEzB,MAAMR,SAASH,GAAGiB,0BAA0B,CAC1CD,eACA,qCAAqC;QACrC,wDAAwD;QACxDd,WACI;YACE,GAAGF,GAAGQ,GAAG;YACTU,eAAcC,KAAK,EAAEC,UAAU,EAAEC,SAAS,EAAEC,SAAS,EAAEC,MAAM;gBAC3D,OAAO;oBAACH,aAAa,CAAC,IAAI,EAAEA,UAAU,CAAC,EAAE,EAAE,GAAG,CAAC,OAAO,CAAC;iBAAC;YAC1D;QACF,IACApB,GAAGQ,GAAG,EACVZ,KAAK4B,OAAO,CAACvB;QAGf,IAAIE,OAAOsB,MAAM,EAAE;YACjBtB,OAAOsB,MAAM,GAAGtB,OAAOsB,MAAM,CAACC,MAAM,CAClC,CAAC,EAAEC,IAAI,EAAE,GACP,sCAAsC;gBACtCA,SAAS;QAEf;QAEA,KAAIxB,iBAAAA,OAAOsB,MAAM,qBAAbtB,eAAeyB,MAAM,EAAE;YACzB,MAAM,qBAEL,CAFK,IAAI/B,WACRG,GAAGe,gBAAgB,CAACZ,OAAOsB,MAAM,CAAC,EAAE,EAAErB,yBADlC,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,OAAOD;IACT,EAAE,OAAO0B,KAAK;QACZ,IAAI/B,QAAQ+B,QAAQA,IAAIC,IAAI,KAAK,eAAe;YAC9C,MAAMC,SAAS,OAAQF,CAAAA,IAAIG,OAAO,IAAI,EAAC;YACvC,MAAM,qBAOL,CAPK,IAAInC,WACRJ,KACE,oBACEC,KAAK,mBACL,MACA,+DACAqC,SANA,qBAAA;uBAAA;4BAAA;8BAAA;YAON;QACF;QACA,MAAMF;IACR;AACF"}